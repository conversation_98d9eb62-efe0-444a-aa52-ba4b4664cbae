{"cells": [{"cell_type": "code", "execution_count": 8, "id": "d768189a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "0f6a138b", "metadata": {}, "outputs": [], "source": ["p = '/mnt/mfs1/class2/0.ecomap/0.process/ValSamples_hh20250519/sample_hh_20250519.csv'"]}, {"cell_type": "code", "execution_count": 3, "id": "c43ea19f", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(p, encoding='utf-8')"]}, {"cell_type": "code", "execution_count": 9, "id": "207d06fc", "metadata": {}, "outputs": [], "source": ["unique_values = np.unique(df['rs_code'],return_counts=True)  # Replace 'column_name' with the actual column name you want to check"]}, {"cell_type": "code", "execution_count": 11, "id": "8f542ec8", "metadata": {}, "outputs": [], "source": ["unique_values_df = pd.DataFrame(data={'rs_code': unique_values[0], 'count': unique_values[1]})"]}, {"cell_type": "code", "execution_count": 15, "id": "9974d05f", "metadata": {}, "outputs": [], "source": ["goal_df = pd.read_csv('/mnt/mfs1/class2/0.ecomap/0.process/ValSamples_hh20250519/resample_goal.csv', encoding='utf-8')"]}, {"cell_type": "code", "execution_count": 19, "id": "8ca1eaab", "metadata": {}, "outputs": [], "source": ["sublist = []\n", "for _,row in goal_df.iterrows():\n", "    rs_code = row['rs_code']\n", "    goal = row['goal']\n", "    sub_df = df.loc[df['rs_code'] == rs_code].sample(n=goal, random_state=1)\n", "    sublist.append(sub_df)\n", "result_df = pd.concat(sublist, ignore_index=True).sample(frac=1, random_state=1)\n", "result_df['PLOTID'] = np.arange(1, len(result_df) + 1)\n", "result_df.to_csv('/mnt/mfs1/class2/0.ecomap/0.process/ValSamples_hh20250519/resample_points.csv', index=False, encoding='utf-8')\n"]}], "metadata": {"kernelspec": {"display_name": "conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}