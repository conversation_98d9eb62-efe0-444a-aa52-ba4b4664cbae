
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime
from pathlib import Path
from rasterio.features import rasterize
from rasterstats import zonal_stats
from pprint import pprint
namemap = {30:'草地',61:'沙地'}
import numpy as np
import itertools

def elevation_histogram(x):
    # bin_edges = [0.05, 0.1, 0.15, 0.2, 0.25, 0.3,1]
    bin_edges = [2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000, 6500, 7000, 7500, 8000, 8500, 9000, 9500, 10000]
    hist, _ = np.histogram(x, bins=bin_edges)
    data = {}  
    for upper, lower, value in zip(bin_edges, bin_edges[1:], hist):
        key = "{:.2f} to {:.2f}".format(upper/10000, lower/10000)
        data[key] = value
    return data

def grassmean(x):
    return np.mean(x[x>0.08])

# def grassmean(x):
#     print(f"DEBUG: Input array x to grassmean: {x}")
#     print(f"DEBUG: Data type of x: {x.dtype}")
#     print(f"DEBUG: Min value in x: {np.min(x) if x.size > 0 else 'empty'}")
#     print(f"DEBUG: Max value in x: {np.max(x) if x.size > 0 else 'empty'}")

#     filtered_x = x[x > 0.08]
#     print(f"DEBUG: Filtered array x[x > 0.08]: {filtered_x}")

#     if filtered_x.size == 0:
#         print("DEBUG: filtered_x is empty. No values > 0.08. Returning np.nan")
#         return np.nan # 或者返回0，或者其他你认为合适的标记

#     mean_val = np.mean(filtered_x)
#     print(f"DEBUG: Calculated mean_val: {mean_val}")

#     # 临时在这里加一个断言，如果均值小于0.08且filtered_x不为空，程序会报错，帮助定位
#     if filtered_x.size > 0 and mean_val <= 0.08:
#          print(f"ERROR Condition: Mean ({mean_val}) is <= 0.08 but filtered_x is not empty and min value is {np.min(filtered_x)}")
#          # raise ValueError(f"Mean {mean_val} is <= 0.08 for non-empty filtered array: {filtered_x}")

#     return mean_val

def sta(y, out_path):
    os.chdir(out_path)

    shp = '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/rep.shp'

    # os.system("sleep 10")
    stat = zonal_stats(shp, path.join(out_path, f"{y}_patch.tif"), categorical=True)[0]
    stat = {namemap[k]:v for k,v in stat.items()}
    stat_ndvi = zonal_stats(shp, path.join(out_path, f"ndvi_{y}_patch.vrt"), copy_properties=True,
                    add_stats={'ndvi_histogram': elevation_histogram,'grassmean':grassmean}, stats=['mean'])[0]
    # print(stat_ndvi)
    # stat.update(stat_ndvi)
    stat.update(stat_ndvi['ndvi_histogram'])
    
    stat_km2 = {k:v*900/1000000 for k,v in stat.items()}
    stat_km2.update({'grass_mean_ndvi':stat_ndvi['grassmean']/10000})
    stat_km2.update({'all_mean_ndvi':stat_ndvi['mean']/10000})
    # for 
    return {y:stat_km2}

def sta_cate(y, out_path):
    os.chdir(out_path)

    shp = '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/rep.shp'

    # os.system("sleep 10")
    stat = zonal_stats(shp, path.join(out_path, f"{y}_patch.tif"), categorical=True)[0]
    stat = {namemap[k]:v for k,v in stat.items()}

    stat_km2 = {k:v*900/1000000 for k,v in stat.items()}

    # for 
    return {y:stat_km2}

if __name__ =='__main__':
    # years = [2000, 2005, 2010, 2012, 2015, 2017, 2022, 2024]
    years = [2000, 2005, 2010, 2012, 2015, 2017, 2022, 2024]

    out_path = '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/reclass_lc_0.2'


    p = Pool(9)
    result = p.imap(partial(sta,out_path=out_path), years)
    p.close()
    p.join()
    result = list(result)
    pprint(result)

    flattened_data = {year: data_dict for entry in result for year, data_dict in entry.items()}
    # flattened_data = {}
    # 转换为 DataFrame
    df = pd.DataFrame.from_dict(flattened_data, orient='index')
    df.to_csv(path.join(out_path, 'grass1.csv'), index=True, header=True, encoding='gbk')
    # 打印 DataFrame
    print(df)