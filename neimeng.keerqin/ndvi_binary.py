
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime
from pathlib import Path
from rasterio.features import rasterize
from rasterstats import zonal_stats

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx



def processRow(row, paras, out_path):
    # GRASS_ID = [201,202,203,204,205]
    years, ndvi_root, base_cls, mask_labels, shp = paras
    pid = os.getpid()
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    logging.info(f"Process {pid} is starting {name}")
    
    with rio.open(base_cls[0]) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        # height, width = src.shape
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['OVERVIEW_RESAMPLING']='NEAREST'
    
    

    base_cls_array_list = [np.isin(get_tiled_imarray(i, xmin, ymin, xmax, ymax)[0], i_label) for i, i_label in zip(base_cls, mask_labels)]

    base_cls_array = np.stack(base_cls_array_list, axis=0).all(axis=0).astype(np.uint8)


    gdf = gpd.read_file(shp)
    gdf = gdf.to_crs(meta['crs'])
    gdf = gdf.cx[xmin:xmax, ymin:ymax]

    shapes = list(zip(gdf.geometry, [1]*len(gdf)))
    if len(shapes) < 1:
        return
    
    burned = rasterize(
        shapes, 
        out_shape=base_cls_array.shape,
        transform=meta['transform'],
        dtype=np.uint8)
    
    base_cls_array[burned ==0] = 0

    if base_cls_array.max() < 1:
        return
    
    
    for y in years:
        oname = path.join(out_path, f'{y}_{name}.tif')
        # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
        if path.exists(oname):
            return
        # ndvi_path = Path(ndvi_root) / f'ndvip90std_keerqin_{y}.vrt'
        ndvi_path = Path(ndvi_root) / f'ndvi_{y}.vrt'
        ndvi_array = get_tiled_imarray(str(ndvi_path), xmin, ymin, xmax, ymax)
        ndvip90_array = ndvi_array[0]
        # ndvistd_array = ndvi_array[1]
        ndvip90_array[base_cls_array == 0] = 0
        # ndvistd_array[base_cls_array == 0] = 0
        result = np.zeros_like(base_cls_array)
        sandmask = ndvip90_array<2000
        grassmask = ndvip90_array>=2000
        result[sandmask] = 61
        result[grassmask] = 30
        result[base_cls_array == 0] = 0
        # ndvi_paths = [Path(ndvi_root) / f'ndvip90std_keerqin_{year}.vrt' for year in years]

        # ndvip90_arrays = [get_tiled_imarray(str(p), xmin, ymin, xmax, ymax)[0] for p in ndvi_paths]
        # ndvistd_arrays = [get_tiled_imarray(str(p), xmin, ymin, xmax, ymax)[1] for p in ndvi_paths]
        
        if result.max() < 1:
            return

        meta['height'], meta['width'] = result.shape
        
        with rio.open(oname,'w',**meta) as dst:
            dst.write(result,1)
        logging.info(f"Process {pid} finished {name}")

        meta_ndvi = meta.copy()
        meta_ndvi.update({'dtype':'int16'})
        oname_ndvi = path.join(out_path, f'ndvi_{y}_{name}.tif')
        if path.exists(oname_ndvi):
            return
        
        with rio.open(oname_ndvi,'w',**meta_ndvi) as dst:
            dst.write(ndvip90_array, 1)



def merge_year(y, out_path):
    os.chdir(out_path)
    cmd = f'gdalbuildvrt -srcnodata 0 ndvi_{y}_patch.vrt ndvi_{y}_*.tif'
    os.system(cmd)
    cmd = f'gdalbuildvrt -srcnodata 0 {y}_patch.vrt {y}_*.tif'
    os.system(cmd)
    
    cmd = f'gdal_translate -of COG -co OVERVIEW_RESAMPLING=NEAREST -co OVERVIEWS=IGNORE_EXISTING {y}_patch.vrt {y}_patch.tif && sleep 5'
    os.system(cmd)
    os.remove(f'{y}_patch.vrt')
    # shp = '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/rep.shp'

    # # os.system("sleep 10")
    # stat = zonal_stats(shp, path.join(out_path, f"{y}_patch.tif"), categorical=True)
    # stat_km2 = {k:v/1000000 for k,v in stat[0].items()}
    # # for 
    # print(y, stat_km2)


if __name__ =='__main__':
    # years = [2000, 2005, 2010, 2012, 2015, 2017, 2022, 2024]
    years = [2000, 2005, 2010, 2012, 2015, 2017, 2022, 2024]

    # ndvi_root = '/mnt/mfs/TRANS/20250514/'
    ndvi_root = '/mnt/mfs/TRANS/ndvi_p90_JuneSep_keerqin/'
    base_cls = [
        '/mnt/mfs1/class2/0.ecomap/0.cn_result/2023_lc_30m.tif',
        '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/krq.tif',
        ]
    mask_labels = [
        (201, 202, 203, 204, 602, 603, 604),
        # (31,32,33,61),
        ]
    shp = '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/2.shp'
    out_path = '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/reclass_lc_0.2'
    if not path.exists(out_path):
        makedirs(out_path)

    # 配置日志
    log_filename = path.join(out_path, f"log_{datetime.now().strftime('%Y%m%d')}.log")  # 日志文件按日期命名
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
        datefmt="%Y-%m-%d %H:%M",  # 日期格式精确到分钟
        handlers=[
            logging.FileHandler(log_filename, mode='a', encoding='utf-8'),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.neimeng/0.shp/keerqin/sub_240x1024m.gpkg"

    gdf = gpd.read_file(tile_def)

    # for i, prop in gdf.iterrows():
    #     processRow((i, prop), paras=(years, ndvi_root, base_cls, mask_labels, shp), out_path=out_path)
    # processRow((5,gdf.iloc[5]), paras=(years, ndvi_root, base_cls, mask_labels, shp), out_path=out_path)

    p = Pool(9)
    p.imap(partial(processRow, paras=(years, ndvi_root, base_cls, mask_labels, shp), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)

    p = Pool(9)
    p.imap(partial(merge_year,out_path=out_path), years)
    p.close()
    p.join()
    # for y in years:
    #     cmd = f'gdalbuildvrt {y}_patch.vrt {y}_*.tif'
    #     os.system(cmd)
    #     cmd = f'gdal_translate -of COG -co OVERVIEW_RESAMPLING=NEAREST -co OVERVIEWS=IGNORE_EXISTING {y}_patch.vrt {y}_patch.tif'
    #     os.system(cmd)
    #     os.remove(f'{y}_patch.vrt')
    # cmd = 'gdalbuildvrt patch302.vrt *.tif'
    # os.system(cmd)