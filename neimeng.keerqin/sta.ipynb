{"cells": [{"cell_type": "code", "execution_count": 2, "id": "34c325b0", "metadata": {}, "outputs": [], "source": ["import rasterio as rio\n", "\n", "from rasterio.features import rasterize\n", "from rasterstats import zonal_stats\n", "from pprint import pprint\n", "\n", "import numpy as np\n"]}, {"cell_type": "code", "execution_count": null, "id": "e384efae", "metadata": {}, "outputs": [], "source": ["shp = '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/rep.shp'\n", "\n", "# os.system(\"sleep 10\")\n", "stat = zonal_stats(shp, '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/krq.tif', categorical=True)[0]\n", "namemap = {31:'高覆盖草地',32:'中覆盖草地',33:'低覆盖草地',61:'沙地'}\n", "stat = {namemap[k]:v for k,v in stat.items() if k in namemap.keys()}\n", "stat_km2 = {k:v*900/1000000 for k,v in stat.items()}\n", "pprint(stat_km2)"]}, {"cell_type": "code", "execution_count": 5, "id": "f15473e0", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'高覆盖草地': 16799.9364, '中覆盖草地': 2978.5356, '低覆盖草地': 514.5156, '沙地': 5776.8093}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["stat_km2"]}, {"cell_type": "code", "execution_count": 6, "id": "fabf8a5a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.10/site-packages/rasterstats/io.py:335: NodataWarning: Setting nodata to -999; specify nodata explicitly\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'沙漠': 19.0863,\n", " '稀疏草地': 4120.2918,\n", " '草丛': 1.1214,\n", " '草原': 30313.7793,\n", " '草甸': 340.3134,\n", " '裸土': 0.0135}\n"]}], "source": ["shp = '/mnt/mfs1/class2/0.ecomap/0.neimeng/keerqin/rep.shp'\n", "\n", "# os.system(\"sleep 10\")\n", "stat = zonal_stats(shp, '/mnt/mfs1/class2/0.ecomap/0.neimeng/0.base/2023_lc_30m_.tif', categorical=True)[0]\n", "namemap = {201:'草原',202:'草甸',203:'草丛',204:'稀疏草地',602:'裸岩',603:'裸土',604:'沙漠'}\n", "stat = {namemap[k]:v for k,v in stat.items() if k in namemap.keys()}\n", "stat_km2 = {k:v*900/1000000 for k,v in stat.items()}\n", "pprint(stat_km2)"]}], "metadata": {"kernelspec": {"display_name": "conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}