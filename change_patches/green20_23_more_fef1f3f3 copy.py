
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters


def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):

    cls_2020, cls_2023, ccd = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    oname = path.join(out_path, name + '.tif')

    if path.exists(oname):
        return
    
    with rio.open(cls_2020) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['dtype'] = 'uint16'
        meta['OVERVIEW_RESAMPLING'] = 'NEAREST'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    # union_recls_array = np.zeros_like(cls_array)
    green_array = get_tiled_imarray(ccd, xmin, ymin, xmax, ymax)[0]
    # update_pv_array = get_tiled_imarray(update_pv, xmin, ymin, xmax, ymax)[0]
    cls20_array = get_tiled_imarray(cls_2020, xmin, ymin, xmax, ymax)[0]
    cls23_array = get_tiled_imarray(cls_2023, xmin, ymin, xmax, ymax)[0]
    
    
    greenmask = green_array > 2020
    greenmask = greenmask & (~np.isin(cls20_array, [301, 302, 101,102,103,104, 105]))
    greenmask = greenmask & (~np.isin(cls23_array, [601, 602, 603, 604, 605, 606, 501, 502, 503, 504, 401, 402, 403, 404, 405, 406, 407, 301, 302]))
    greenmask = greenmask & (cls20_array != cls23_array)

    if greenmask.max() < 1:
        return
    meta['height'], meta['width'] = greenmask.shape
    
    with rio.open(oname,'w',**meta) as dst:
        dst.write(greenmask,1)




if __name__ =='__main__':

    cls_2020 = '/mnt/mfs/TRANS/longtf/lc_2020/index.vrt'
    cls_2023 = '/mnt/mfs/TRANS/longtf/lc_2023/index.vrt'
    ccd = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    
    # allow_src_dict = {
    #     'is':[107, 201, 203, 204, 302],
    #     'pv':[201, 203, 204, 302],
    # }

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/green2020_2023_more_fef1f3f3'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(cls_2020,cls_2023, ccd), out_path=out_path)

    p = Pool(56)
    p.imap(partial(processRow, paras=(cls_2020,cls_2023, ccd), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt green20_23.vrt *.tif'
    os.system(cmd)