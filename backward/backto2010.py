
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    base10, base20, new20, allow_list = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    oname = path.join(out_path, name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    with rio.open(new20) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    # union_recls_array = np.zeros_like(cls_array)
    
    new20_array = get_tiled_imarray(new20, xmin, ymin, xmax, ymax)[0]

    if new20_array.max() < 1:
        return
    
    base20_array = get_tiled_imarray(base20, xmin, ymin, xmax, ymax)[0]
    base10_array = get_tiled_imarray(base10, xmin, ymin, xmax, ymax)[0]

    unchangeMask = base20_array == base10_array

    backmask = unchangeMask & np.isin(new20_array,allow_list)

    base10_array[backmask] = new20_array[backmask]

    
    meta['height'], meta['width'] = base10_array.shape
    
    with rio.open(oname,'w',**meta) as dst:
        dst.write(base10_array,1)




if __name__ =='__main__':
    # raster_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # raster_ref_path = '/mnt/mfs/TRANS/longtf/landsat_2020/2020.vrt'
    # change_mask_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # shp_path = '/mnt/Netapp/longtf/!eco.system/changes/cn_2023_yellow_river/N14E15_change.shp'
    # mask_path = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    base10 = '/mnt/Netapp/longtf/!eco.system/landcover/lc_hh_2010.tif'
    base20 = '/mnt/Netapp/longtf/!eco.system/landcover/lc_hh_2020.tif'
    new20 = '/mnt/mfs1/class2/0.ecomap/0.process/stacks/2020a1bc.tif'

    allow_list = [102, 107, 201, 203, 204, 302]

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/backto2010'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(base10, base20, new20, allow_list), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(base10, base20, new20, allow_list), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt a1bc.2010.back.vrt *.tif'
    os.system(cmd)