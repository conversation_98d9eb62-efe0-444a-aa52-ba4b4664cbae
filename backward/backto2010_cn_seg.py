
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx


def processRow(row, paras, out_path):
    # GRASS_ID = [201,202,203,204,205]
    pid = os.getpid()
    
    base10, base20, new20, ccd, cisc10, update_pv, change_seg, allow_list = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    logging.info(f"Process {pid} is starting {name}")

    oname_cls = path.join(out_path, 'cls_'+name + '.tif')
    oname_patch = path.join(out_path, 'patch_'+name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname_cls) & path.exists(oname_patch):
        return
    
    with rio.open(new20) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['OVERVIEW_RESAMPLING']='NEAREST'

    e = (xmin, ymin, xmax, ymax)
    new20_array = get_tiled_imarray(new20, xmin, ymin, xmax, ymax)[0]

    if new20_array.max() < 1:
        return
    
    # base20_array = get_tiled_imarray(base20, xmin, ymin, xmax, ymax)[0]
    base10_array = get_tiled_imarray(base10, xmin, ymin, xmax, ymax)[0]
    ccd_array = get_tiled_imarray(ccd, xmin, ymin, xmax, ymax)[0]
    cisc10_array = get_tiled_imarray(cisc10, xmin, ymin, xmax, ymax)[0]
    update_pv_array = get_tiled_imarray(update_pv, xmin, ymin, xmax, ymax)[0]
    change_seg_array = get_tiled_imarray(change_seg, xmin, ymin, xmax, ymax)[0]

    # unchangeMask = (base20_array == base10_array) & (ccd_array < 1)  
    unchangeMask = ~((change_seg_array==1) | (ccd_array==1))
    old10 = base10_array.copy()


    backmask = unchangeMask & np.isin(new20_array,allow_list)

    base10_array[backmask] = new20_array[backmask]

    update_cisc_array = cisc10_array==1
    base503 = base10_array==503
    update_cisc_array = morphology.remove_small_objects(update_cisc_array, 3, 2)
    near503 = morphology.binary_dilation(base503, morphology.square(4))
    new503 = update_cisc_array & base503
    potential503 = update_cisc_array & (morphology.binary_opening(update_cisc_array&(new503==0), morphology.square(3))==0)
    new503 = potential503 & near503
    del base503, potential503, near503

    base10_array[update_cisc_array] = 501
    base10_array[new503] = 503

    update_pv_array = (update_pv_array <= 2010) & (update_pv_array > 1000)
    update_pv_array = morphology.binary_closing(update_pv_array, morphology.disk(2))
    update_pv_array = morphology.remove_small_objects(update_pv_array, 3)
    pv_deny = [101,102,103,104,105,106,107,108,111,112,113,114,501,503]
    base10_array[update_pv_array & (~np.isin(base10_array, pv_deny))] = 502

    future501 = new20_array==501
    cu501 = base10_array==501
    doubt_is = cu501 & (~future501)
    degrade_cis = morphology.binary_dilation(future501, morphology.square(5)) & doubt_is
    local_neighber_id = filters.rank.modal(base10_array, morphology.disk(5), mask = ~np.isin(base10_array, [501,502,503]))
    base10_array[degrade_cis] = local_neighber_id[degrade_cis]


    patch10 = base10_array.copy()
    patch10[patch10 == old10] = 0

    
    # if valid_mask.max() < 1:
    #     return
    meta['height'], meta['width'] = patch10.shape
    
    with rio.open(oname_cls,'w',**meta) as dst, rio.open(oname_patch,'w',**meta) as dst_patch:
        dst.write(base10_array,1)
        dst_patch.write(patch10,1)
    logging.info(f"Process {pid} finished {name}")


if __name__ =='__main__':
    # valid2022 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/valid2m/valid2m.vrt'
    # valid2020 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/valid2m_2020/valid2m.vrt'
    # update_cisc2020 = '/mnt/mfs1/classT/0.Dataset/CISC_2020/isc_hh.vrt'
    # update_cisc2022 = '/mnt/mfs1/classT/0.Dataset/CISC_2022/isc2022_cn_ecolc.vrt'
    update_pv = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/pv_change/pv_change.vrt'

    # base_cls = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt'
    # ref2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt'

    # prob20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2020_hh_prob_1/prob2020.vrt'
    # prob23 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2023_hh_prob_1/prob2023.vrt'

    # ccd = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    # update_ndvi = '/mnt/mfs1/classT/0.DataCollection/composits/ChinaYearlyNoSnow_indexes_p85/index85_hh.vrt'


    # initpatch_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patch302_clean_1230/patch302.vrt'
    base10 = '/mnt/Netapp/longtf/!eco.system/landcover/base/2010lc_30m_type2015.tif'
    # base20 = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.tif'
    base20 = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt'
    new20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patches_2020_fixISdrop/cls_2020.vrt'
    ccd = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2010_2020/index.vrt'
    change_seg = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/2010_2020_seg_mask.tif'
    cisc10 = '/mnt/Netapp/yinry/dynamicDig/tfapp/PREDS/isd_yearly/2010/2010isc_hh.vrt'

    allow_list = [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 
                 201, 202, 203, 204, 205,
                 301, 302]

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patches_2010_seg_longtf_fixdrop'
    paras = (base10, base20, new20, ccd, cisc10, update_pv, change_seg, allow_list)
    if not path.exists(out_path):
        makedirs(out_path)

    # 配置日志
    log_filename = path.join(out_path, f"log_{datetime.now().strftime('%Y%m%d')}.log")  # 日志文件按日期命名
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
        datefmt="%Y-%m-%d %H:%M",  # 日期格式精确到分钟
        handlers=[
            logging.FileHandler(log_filename, mode='a', encoding='utf-8'),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    # for i, prop in gdf.iterrows():
    #     processRow((i, prop), paras=paras, out_path=out_path)
    # # processRow((0,gdf.iloc[0]), paras=(im), out_path=out_path)

    p = Pool(45)
    p.imap(partial(processRow, paras=paras, out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt cls_2010.vrt cls*.tif'
    os.system(cmd)
    cmd = 'gdalbuildvrt patch_2010.vrt patch*.tif'
    os.system(cmd)