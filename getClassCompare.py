
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    cls_path_list, cls_map_list, ignore_list = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    encode_oname = path.join(out_path, 'encode_' + name + '.tif')
    score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(encode_oname)and path.exists(score_oname):
        return
    
    with rio.open(cls_path_list[0]) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    recls_array_list = []
    
    for i in range(len(cls_path_list)):
        cls_array = get_tiled_imarray(cls_path_list[i], xmin, ymin, xmax, ymax)[0]
        if i == 0:
            ignore_mask = np.zeros_like(cls_array, dtype=np.uint8)
        
        recls_array = np.zeros_like(cls_array)
        recls_array[np.isin(cls_array, cls_map_list[i])] = 1
        recls_array_list.append(recls_array)
        ignore_mask[np.isin(cls_array, ignore_list[i])] = 1
    recls_array_bits = np.stack(recls_array_list)
    positive_count = np.sum(recls_array_bits, axis=0)
    diff_score = np.sin(positive_count/(len(cls_path_list))*np.pi)

    diff_score[ignore_mask==1] = 0

    assert recls_array_bits.shape[0] <= 8 

    encode_array = np.packbits(recls_array_bits, axis=0)[0]
    encode_array[ignore_mask==1] = 0
    
    meta['height'], meta['width'] = recls_array_bits.shape[1:]
    
    with rio.open(encode_oname,'w',**meta) as dst:
        dst.write(encode_array,1)

    meta['dtype'] = 'float32'
    with rio.open(score_oname,'w',**meta) as dst:
        dst.write(diff_score,1)



if __name__ =='__main__':
    # raster_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # raster_ref_path = '/mnt/mfs/TRANS/longtf/landsat_2020/2020.vrt'
    # change_mask_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # shp_path = '/mnt/Netapp/longtf/!eco.system/changes/cn_2023_yellow_river/N14E15_change.shp'
    # mask_path = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    # cls_path = '/mnt/Netapp/longtf/!eco.system/landcover/2010lc_30m_type2015.tif'
    cls_path_list = [
        '/mnt/Netapp/longtf/!eco.system/landcover/lc_hh_2020.tif',
        '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt',
        ]
    cls_map_list = [
        [301, 302], 
        [301, 302]
    ]

    ignore_list = [
        [],
        [700]
    ]


    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/compare1110_cn'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    # processRow((0,gdf.iloc[0]), paras=(cls_path_list, cls_map_list, ignore_list), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(cls_path_list, cls_map_list, ignore_list), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()