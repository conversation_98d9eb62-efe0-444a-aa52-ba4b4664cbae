
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
from sklearn.neighbors import KNeighborsClassifier
from tqdm import tqdm
from skimage import morphology

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in tqdm(range(0, lasty, step)):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    for xstart in range(0,lastx, step):
        yield lasty, xstart
    yield lasty, lastx

def updateRow(row, raster_ref_path, cls_path, error_path, shade_path, out_path, wsize=180,step=80, n_neighbors=10):
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    inner_name = path.join(error_path, 'inner_' + name + '.tif')
    among_name = path.join(error_path, 'among_'+ name + '.tif')

    cls_oname = path.join(out_path, name + '.tif')
    mask_oname = path.join(out_path, 'mask_' + name + '.tif')
    if path.exists(cls_oname) and path.exists(mask_oname):
        return
    
    with rio.open(cls_path) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['OVERVIEW_RESAMPLING']='NEAREST'
    im_array = get_tiled_imarray(raster_ref_path, xmin, ymin, xmax, ymax)
    cls_array = get_tiled_imarray(cls_path, xmin, ymin, xmax, ymax)
    inner_error_array = get_tiled_imarray(inner_name, xmin, ymin, xmax, ymax)
    among_error_array = get_tiled_imarray(among_name, xmin, ymin, xmax, ymax)
    shade_array = get_tiled_imarray(shade_path, xmin, ymin, xmax, ymax)
    
    meta['height'], meta['width'] = im_array.shape[1:]
    cls_ids = np.unique(cls_array)
    cls_index_series = pd.Series(np.arange(len(cls_ids)), index=cls_ids)

    # 定义形态学操作的结构元素
    selem = morphology.disk(2)  # 结构元素，这里用的是半径为5的圆盘

    updatemask = (inner_error_array>0.15) | (among_error_array<0.35)
    mask = cls_array==0
    nanmask = np.isnan(im_array).any(axis=0)
    satumask = (im_array[0]>0.6) & (cls_array[0]!=606)&(cls_array[0]!=501)
    shade_mask = shade_array[0]<200
    allmask = mask|morphology.dilation(nanmask|satumask|shade_mask, selem)

    # 先进行膨胀操作
    dilated_image = morphology.dilation(updatemask[0], selem)

    # 再进行腐蚀操作
    updatemask[0] = morphology.erosion(dilated_image, selem)
    updatemask[allmask] = False
    updatemask[0] = morphology.remove_small_objects(updatemask[0], 8, 2)
    unchangemask = ~(updatemask|allmask)

    probs_array = np.zeros((len(cls_ids), meta['height'], meta['width']))

    for ystart, xstart in slides_of_imarray(im_array, wsize, step):
        win_im = im_array[:, ystart:ystart+wsize, xstart:xstart+wsize]
        win_cls = cls_array[0, ystart:ystart+wsize, xstart:xstart+wsize]
        if win_cls.max() == 0:
            continue
        win_updatemask = updatemask[0, ystart:ystart+wsize, xstart:xstart+wsize]
        if win_updatemask.sum() < 1:
            continue
        # win_mask = mask[0, ystart:ystart+wsize, xstart:xstart+wsize]
        # win_nanmask = nanmask[ystart:ystart+wsize, xstart:xstart+wsize]
        # win_satumask = satumask[ystart:ystart+wsize, xstart:xstart+wsize]
        this_unchange = unchangemask[0, ystart:ystart+wsize, xstart:xstart+wsize]
        # win_nanmask = np.isnan(win_im).any(axis=0)

        # satumask = win_im[0]>1
        # this_unchange = ~(win_updatemask|win_mask|win_nanmask|win_satumask)
        if this_unchange.sum() < 200:
            continue
        m = KNeighborsClassifier(n_neighbors=n_neighbors, weights='distance')
        m.fit(win_im[:,this_unchange].T, win_cls[this_unchange])
        probs = m.predict_proba(win_im[:,win_updatemask].T)
        this_cls_index =  cls_index_series[m.classes_]
        this_prob = probs_array[this_cls_index,ystart:ystart+wsize, xstart:xstart+wsize]
        this_prob[:,win_updatemask] += probs.T
        probs_array[this_cls_index,ystart:ystart+wsize, xstart:xstart+wsize] = this_prob
        # probs_array[this_cls_index,win_updatemask] += probs.T
    new_cls_index = probs_array.argmax(axis=0)
    new_cls_id = cls_ids[new_cls_index[updatemask[0]]]
    cls_array[updatemask] = new_cls_id
    with rio.open(cls_oname,'w',**meta) as dst:
        dst.write(cls_array)
    with rio.open(mask_oname,'w',**meta) as dst:
        dst.write(updatemask)



if __name__ =='__main__':
    # raster_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    raster_ref_path = '/mnt/mfs/TRANS/longtf/landsat_2020/2020.vrt'
    # change_mask_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # shp_path = '/mnt/Netapp/longtf/!eco.system/changes/cn_2023_yellow_river/N14E15_change.shp'
    # mask_path = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    cls_path = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020_update/2020.vrt'
    error_path = '/mnt/mfs1/class2/0.ecomap/0.process/cls_error_2020'
    shade_path = '/mnt/mfs/TRANS/0.eco_shade/shade.vrt'
    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/cls_update_2020_0.2_0.35_morph_shade_sat0.6_rm8'
    if not path.exists(out_path):
        makedirs(out_path)


    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/300km.shp"

    gdf = gpd.read_file(tile_def)

    # for row in gdf.iterrows():
    #     updateRow(row, raster_ref_path=raster_ref_path, cls_path=cls_path, error_path=error_path, shade_path=shade_path, out_path=out_path)
    
    p = Pool(65)
    p.imap(partial(updateRow, raster_ref_path=raster_ref_path, cls_path=cls_path, error_path=error_path, shade_path=shade_path, out_path=out_path), gdf.iterrows())
    p.close()
    p.join()