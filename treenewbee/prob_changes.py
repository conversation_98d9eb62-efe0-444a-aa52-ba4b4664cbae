
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    prob20_path, prob23_path = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    encode_oname = path.join(out_path, 'intersect_' + name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(encode_oname):
        return
    
    with rio.open(prob20_path) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    # union_recls_array = np.zeros_like(cls_array)
    

    p20_array = get_tiled_imarray(prob20_path, xmin, ymin, xmax, ymax)[0]
    p23_array = get_tiled_imarray(prob23_path, xmin, ymin, xmax, ymax)[0]
    if p23_array.max() < 0.1:
        return
    result = np.sqrt((p23_array-p20_array)*p23_array)

    meta['height'], meta['width'] = result.shape
    
    with rio.open(encode_oname,'w',**meta) as dst:
        dst.write(result,1)




if __name__ =='__main__':
    prob20_path = '/mnt/mfs1/class2/0.ecomap/0.process/treenew2020_hh_prob_1/prob2020.vrt'
    prob23_path = '/mnt/mfs1/class2/0.ecomap/0.process/treenew2023_hh_prob_1/prob2023.vrt'
 
    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/treenew_probchange'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(prob20_path, prob23_path), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(prob20_path, prob23_path), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt treenew_probchange.vrt *.tif'
    os.system(cmd)