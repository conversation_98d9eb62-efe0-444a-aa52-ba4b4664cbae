
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    treenew_path, new2023_cls, update_cisc, update_pv = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    encode_oname = path.join(out_path, name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(encode_oname):
        return
    
    with rio.open(treenew_path) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['dtype'] = 'uint16'
        meta['OVERVIEW_RESAMPLING'] = 'NEAREST'
        
    bi_array = get_tiled_imarray(treenew_path, xmin, ymin, xmax, ymax)[0]

    

    if bi_array.max() < 0.05:
        return
    
    bi_array = bi_array == 1
    update_cisc_array = get_tiled_imarray(update_cisc, xmin, ymin, xmax, ymax)[0]
    update_pv_array = get_tiled_imarray(update_pv, xmin, ymin, xmax, ymax)[0]
    base_cls_array = get_tiled_imarray(new2023_cls, xmin, ymin, xmax, ymax)[0]

    update_pv_array = morphology.binary_closing(update_pv_array, morphology.disk(2))
    bi_array[update_cisc_array==1] = 0
    bi_array[update_pv_array==1] = False

    bi_array[np.isin(base_cls_array, [401, 402, 403, 404, 405, 406, 407])] = False

    bi_array = morphology.binary_closing(bi_array, morphology.square(3))

    def rm_small(bi_array, s):
        rm_small = morphology.binary_opening(bi_array, morphology.disk(s))
        bi_filter = morphology.remove_small_objects(bi_array,int(2*s*3.14))
        line = bi_filter & (~rm_small)
        bi_array[line] = 0
    
    rm_small(bi_array,1)
    rm_small(bi_array,2)

    


    meta['height'], meta['width'] = bi_array.shape
    
    with rio.open(encode_oname,'w',**meta) as dst:
        dst.write(bi_array,1)




if __name__ =='__main__':
    treenew_path = '/mnt/mfs1/class2/0.ecomap/0.process/treenew_probchange_cmask_f3_bi_14/treenew_probchange_bi.vrt'
    new2023_cls = '/mnt/mfs1/class2/0.ecomap/0.process/stacks/2023a1bc.tif'
    update_cisc = '/mnt/mfs1/classT/0.Dataset/CISC/isc_hh.vrt'
    update_pv = '/mnt/Netapp/yinry/0.LCLU.DATA/PV_2020/albers/pv_2020.vrt'

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/treenew_bi_clean0'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(treenew_path, new2023_cls, update_cisc, update_pv), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(treenew_path, new2023_cls, update_cisc, update_pv), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt treenew_bi_clean.vrt *.tif'
    os.system(cmd)