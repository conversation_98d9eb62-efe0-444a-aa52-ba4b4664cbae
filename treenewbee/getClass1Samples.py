
import rasterio as rio
from rasterio.transform import xy
from rasterio.warp import transform as transform_crs
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
import geopandas as gpd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from shapely.geometry import Point

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    cls_dict = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    n_samples = 50

    oname = path.join(out_path, 'sample_' + name + '.shp')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    transform_local = None
    all_geometry = []
    all_id = []
    for k, v in cls_dict.items():
        if transform_local is None:
            with rio.open(v) as src:
                transform = src.transform
                row_start, col_start = src.index(xmin, ymax)
                transform_local = transform * rio.Affine.translation(col_start, row_start)
                crs = src.crs
        cls_array = get_tiled_imarray(v, xmin, ymin, xmax, ymax)[0]
        if cls_array.max()<1:
            continue
        ones_positions = np.argwhere(cls_array == 1)
        if len(ones_positions) >= n_samples:
            selected_indices = np.random.choice(len(ones_positions), n_samples, replace=False)
            selected_positions = ones_positions[selected_indices]
        else:
            selected_positions = ones_positions
        xs, ys = xy(transform_local, selected_positions[:, 0], selected_positions[:, 1])
        longs, lats = transform_crs(crs, 'epsg:4326', xs, ys)
        geometry = [Point(xy) for xy in zip(longs, lats)]
        all_geometry.extend(geometry)
        all_id.extend([k]*len(geometry))
    gdf = gpd.GeoDataFrame(data={'rs_code': all_id}, geometry=all_geometry, crs="EPSG:4326")
    if len(gdf) == 0:
        return
    gdf.to_file(oname)

def thin_points_shapely(xs, ys, k):
    """
    使用 shapely 抽稀点集，使得每个点与其他点的距离大于 k。
    
    :param points: 输入点集，格式为 [(x1, y1), (x2, y2), ...]
    :param k: 距离阈值，单位与点坐标一致
    :return: 抽稀后的点集，格式为 [(x1, y1), (x2, y2), ...]
    """
    # 将点转化为 Shapely Point 对象
    point_objects = [Point(p) for p in zip(xs, ys)]
    
    # 按 x 坐标排序（或其他排序方式）
    point_objects.sort(key=lambda p: (p.x, p.y))
    
    # 初始化存储抽稀点的列表
    filtered_points = []
    
    # 遍历所有点，按顺序筛选
    for point in point_objects:
        # 检查是否与现有点的缓冲区相交
        if all(not point.within(fp.buffer(k)) for fp in filtered_points):
            filtered_points.append(point)
    
    # 返回最终结果的坐标
    return [p.x for p in filtered_points], [p.y for p in filtered_points]

def processTile(bounds, oname, cls_path):
    xmin, ymin, xmax, ymax = bounds
    n_samples = 5
    
    with rio.open(cls_path) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        crs = src.crs
    
    cls_array = get_tiled_imarray(cls_path, xmin, ymin, xmax, ymax)[0]
    if cls_array.max()<1:
        return
    
    clist = np.unique(cls_array)
    all_geometry = []
    all_id = []
    for cid in clist:
        if cid == 0: 
            continue
        ones_positions = np.argwhere(cls_array == cid)
        if len(ones_positions) >= n_samples:
            selected_indices = np.random.choice(len(ones_positions), n_samples, replace=False)
            selected_positions = ones_positions[selected_indices]
        else:
            selected_positions = ones_positions
        xs, ys = xy(transform_local, selected_positions[:, 0], selected_positions[:, 1])
        xs, ys = thin_points_shapely(xs, ys, 1000)
        longs, lats = transform_crs(crs, 'epsg:4326', xs, ys)
        geometry = [Point(xy) for xy in zip(longs, lats)]
        all_geometry.extend(geometry)
        all_id.extend([cid]*len(geometry))
    gdf = gpd.GeoDataFrame(data={'rs_code': all_id}, geometry=all_geometry, crs="EPSG:4326")
    if len(gdf) == 0:
        return
    gdf.to_file(oname)



if __name__ =='__main__':
    cls_path = '/mnt/mfs1/class2/0.ecomap/0.process/stacks/2020a1bc.tif'
    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/treenew_sample'
    if not path.exists(out_path):
        makedirs(out_path)
    p = Pool(36)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)
    
    total_xmin, total_ymin, total_xmax, total_ymax = gdf.total_bounds

    step = 1e5
    results = []
    try_step = False
    for c, x in enumerate(np.arange(total_xmin, total_xmax, step)):
        for r, y in enumerate(np.arange(total_ymin, total_ymax, step)):
            oname = path.join(out_path,f'{c}_{r}.shp')
            if path.exists(oname):
                continue
            tbound = (int(x), int(y), int(x+step), int(y+step))
            if try_step:
                processTile(tbound, oname, cls_path)
            else:
                results.append(p.apply_async(processTile, (tbound, oname, cls_path)))
                try_step = False
    output = [result.get() for result in results]
    # os.chdir(out_path)
    # cmd = 'gdalbuildvrt intersect302.vrt *.tif'
    # os.system(cmd)