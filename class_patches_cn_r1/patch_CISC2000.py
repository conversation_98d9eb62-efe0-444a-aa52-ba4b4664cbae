
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters


def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):

    update_cisc, base_cls = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    oname = path.join(out_path, name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    with rio.open(update_cisc) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['dtype'] = 'uint16'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    # union_recls_array = np.zeros_like(cls_array)
    update_cisc_array = get_tiled_imarray(update_cisc, xmin, ymin, xmax, ymax)[0]
    # update_pv_array = get_tiled_imarray(update_pv, xmin, ymin, xmax, ymax)[0]
    base_cls_array = get_tiled_imarray(base_cls, xmin, ymin, xmax, ymax)[0]
    # ref2020_cls_array = get_tiled_imarray(ref2020_cls, xmin, ymin, xmax, ymax)[0]
    update_cisc_array = update_cisc_array==1
    # update_pv_array = update_pv_array==1

    # update_cisc_array = morphology.binary_closing(update_cisc_array, morphology.disk(1))
    # update_pv_array = morphology.binary_closing(update_pv_array, morphology.disk(2))

    base503 = base_cls_array==503
    # update_cisc_array[base503] = False

    update_cisc_array = morphology.remove_small_objects(update_cisc_array, 3, 2)
    # update_pv_array = morphology.remove_small_objects(update_pv_array, 3)

    near503 = morphology.binary_dilation(base503, morphology.square(4))
    new503 = update_cisc_array & base503
    potential503 = update_cisc_array & (morphology.binary_opening(update_cisc_array&(new503==0), morphology.square(3))==0)
    new503 = potential503 & near503
    del base503, potential503, near503

    patch_array = np.zeros_like(update_cisc_array, dtype='uint16')

    patch_array[update_cisc_array] = 501
    # patch_array[update_pv_array] = 502
    patch_array[new503] = 503
    patch_array[base_cls_array==0] = 0
    patch_array[base_cls_array==0] = 0


    if patch_array.max() < 1:
        return
    meta['height'], meta['width'] = patch_array.shape
    
    with rio.open(oname,'w',**meta) as dst:
        dst.write(patch_array,1)




if __name__ =='__main__':
    update_cisc = '/mnt/Netapp/yinry/dynamicDig/tfapp/PREDS/isd_yearly/2000/2000isc_hh.vrt'
    # update_pv = '/mnt/Netapp/yinry/0.LCLU.DATA/PV_2020/albers/pv_2020.vrt'

    base_cls = '/mnt/mfs1/class2/0.ecomap/0.process/stacks/2000a1bc.tif'
    # ref2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt'

    # allow_src_dict = {
    #     'is':[107, 201, 203, 204, 302],
    #     'pv':[201, 203, 204, 302],
    # }

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/patch5xx_2000'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    # processRow((0,gdf.iloc[0]), paras=(update_cisc, base_cls), out_path=out_path)

    p = Pool(56)
    p.imap(partial(processRow, paras=(update_cisc, base_cls), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt patch5xx.vrt *.tif'
    os.system(cmd)