
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime
from sklearn.neighbors import KNeighborsClassifier

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    # GRASS_ID = [201,202,203,204,205]
    pid = os.getpid()
    
    raster_ref_path, new20, ccd, todomask, cisc22, update_pv, shade_path, cisc22_valid_path = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    logging.info(f"Process {pid} is starting {name}")

    oname_cls = path.join(out_path, 'cls_'+name + '.tif')
    oname_patch = path.join(out_path, 'patch_'+name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    # if path.exists(oname_cls) & path.exists(oname_patch):
    #     return
    
    with rio.open(new20) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['OVERVIEW_RESAMPLING']='NEAREST'
    e = (xmin, ymin, xmax, ymax)
    cls_array = get_tiled_imarray(new20, xmin, ymin, xmax, ymax)[0]
    if cls_array.max() < 1:
        return
    # old = cls_array.copy()
    
    cls_ids = np.unique(cls_array)
    cls_index_series = pd.Series(np.arange(len(cls_ids)), index=cls_ids)

    im_array = get_tiled_imarray(raster_ref_path, xmin, ymin, xmax, ymax)
    todomask_array = get_tiled_imarray(todomask, xmin, ymin, xmax, ymax)[0]
    ccd_array = get_tiled_imarray(ccd, xmin, ymin, xmax, ymax)[0]
    cisc22_array = get_tiled_imarray(cisc22, xmin, ymin, xmax, ymax)[0]
    update_pv_array = get_tiled_imarray(update_pv, xmin, ymin, xmax, ymax)[0]
    shade_array = get_tiled_imarray(shade_path, xmin, ymin, xmax, ymax)
    cisc22_valid_array = get_tiled_imarray(cisc22_valid_path, xmin, ymin, xmax, ymax)[0]

    updatemask = (todomask_array==1)
    updatemask[cls_array==0] = 0
    updatemask = morphology.binary_closing(updatemask, morphology.disk(1))
    updatemask = morphology.binary_opening(updatemask, morphology.disk(1))
    updatemask = morphology.remove_small_objects(updatemask, 4, 2)

    update_pv_array = update_pv_array > 2020
    update_pv_array = morphology.binary_closing(update_pv_array, morphology.disk(2))
    update_pv_array = morphology.remove_small_objects(update_pv_array, 3)
    


    cisc22_array[cisc22_valid_array==0] = 0

    update_cisc_array = (cisc22_array==1) & (
        morphology.binary_closing(ccd_array>2020, morphology.disk(2)) | np.isin(cls_array,[501,502,503,504]))
    base503 = cls_array==503
    update_cisc_array = morphology.remove_small_objects(update_cisc_array, 3, 2)
    near503 = morphology.binary_dilation(base503, morphology.square(4))
    new503 = update_cisc_array & base503
    potential503 = update_cisc_array & (morphology.binary_opening(update_cisc_array&(new503==0), morphology.square(3))==0)
    new503 = potential503 & near503
    del base503, potential503, near503
    cls_array[update_cisc_array] = 501
    cls_array[update_pv_array] = 502
    cls_array[new503] = 503

    mask = cls_array==0
    nanmask = np.isnan(im_array).any(axis=0)
    satumask = (im_array[0]>0.6) & (cls_array!=606)&(cls_array!=501)
    shade_mask = shade_array[0]<200
    # 不在无效区域找近邻
    allmask = mask|morphology.dilation(nanmask|satumask|shade_mask, morphology.disk(2))
    # 不修改不透水面、光伏、无效值和过饱和区域
    updatemask[update_cisc_array|update_pv_array|nanmask|satumask] = False
    avoid5mask = (cls_array==501)|(cls_array==503)
    avoid5mask = avoid5mask & (~morphology.binary_erosion(avoid5mask, morphology.disk(11)))
    unchangemask = ~(updatemask|allmask|(ccd_array>2020)|avoid5mask)

    probs_array = np.zeros((len(cls_ids), updatemask.shape[0], updatemask.shape[1]))

    wsize=180
    step=80
    n_neighbors=10
    for ystart, xstart in slides_of_imarray(im_array, wsize, step):
        win_im = im_array[:, ystart:ystart+wsize, xstart:xstart+wsize]
        win_cls = cls_array[ystart:ystart+wsize, xstart:xstart+wsize]
        if win_cls.max() == 0:
            continue
        win_updatemask = updatemask[ystart:ystart+wsize, xstart:xstart+wsize]
        if win_updatemask.sum() < 1:
            continue
        # win_mask = mask[0, ystart:ystart+wsize, xstart:xstart+wsize]
        # win_nanmask = nanmask[ystart:ystart+wsize, xstart:xstart+wsize]
        # win_satumask = satumask[ystart:ystart+wsize, xstart:xstart+wsize]
        this_unchange = unchangemask[ystart:ystart+wsize, xstart:xstart+wsize]
        # win_nanmask = np.isnan(win_im).any(axis=0)

        # satumask = win_im[0]>1
        # this_unchange = ~(win_updatemask|win_mask|win_nanmask|win_satumask)
        if this_unchange.sum() < 200:
            continue
        m = KNeighborsClassifier(n_neighbors=n_neighbors, weights='distance')
        m.fit(win_im[:,this_unchange].T, win_cls[this_unchange])
        probs = m.predict_proba(win_im[:,win_updatemask].T)
        this_cls_index =  cls_index_series[m.classes_]
        this_prob = probs_array[this_cls_index,ystart:ystart+wsize, xstart:xstart+wsize]
        this_prob[:,win_updatemask] += probs.T
        probs_array[this_cls_index,ystart:ystart+wsize, xstart:xstart+wsize] = this_prob
        # probs_array[this_cls_index,win_updatemask] += probs.T

    new_cls_index = probs_array.argmax(axis=0)
    updatemask[probs_array.max(axis=0)<0.3] = False
    new_cls_id = cls_ids[new_cls_index[updatemask]]
    cls_array[updatemask] = new_cls_id

    patch23 = cls_array.copy()
    patch23[todomask_array!=1] = 0

    
    # if valid_mask.max() < 1:
    #     return
    meta['height'], meta['width'] = cls_array.shape
    
    with rio.open(oname_cls,'w',**meta) as dst, rio.open(oname_patch,'w',**meta) as dst_patch:
        dst.write(cls_array,1)
        dst_patch.write(patch23,1)
    logging.info(f"Process {pid} finished {name}")




if __name__ =='__main__':
    valid2022 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/valid2m/valid2m.vrt'
    # valid2020 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/valid2m_2020/valid2m.vrt'
    # update_cisc2020 = '/mnt/mfs1/classT/0.Dataset/CISC_2020/isc_hh.vrt'
    # update_cisc2022 = '/mnt/mfs1/classT/0.Dataset/CISC_2022/isc2022_cn_ecolc.vrt'
    update_pv = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/pv_change/pv_change.vrt'

    # base_cls = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt'
    # ref2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt'

    # prob20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2020_hh_prob_1/prob2020.vrt'
    # prob23 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2023_hh_prob_1/prob2023.vrt'

    ccd = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    # update_ndvi = '/mnt/mfs1/classT/0.DataCollection/composits/ChinaYearlyNoSnow_indexes_p85/index85_hh.vrt'


    # initpatch_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patch302_clean_1230/patch302.vrt'
    # base10 = '/mnt/Netapp/longtf/!eco.system/landcover/base/2010lc_30m_type2015.tif'
    # base20 = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.tif'
    # base20 = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt'
    # new20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patches_2022/cls_2020.vrt'
    new20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn_r1/patches_2020_fixISdrop/cls_2020.vrt'
    cisc22 = '/mnt/mfs1/classT/0.Dataset/CISC_2022/isc2022_cn_ecolc.vrt'
    todomask = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/2023todo_mask.tif'
    shade_path = '/mnt/mfs/TRANS/0.eco_shade/shade.vrt'

    raster_ref_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'

    # allow_list = [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 
    #              201, 202, 203, 204, 205,
    #              301, 302]

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn_r1/patches_fixdrop_2023'
    paras = (raster_ref_path, new20, ccd, todomask, cisc22, update_pv, shade_path, valid2022)
    if not path.exists(out_path):
        makedirs(out_path)

    # 配置日志
    log_filename = path.join(out_path, f"log_{datetime.now().strftime('%Y%m%d')}.log")  # 日志文件按日期命名
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
        datefmt="%Y-%m-%d %H:%M",  # 日期格式精确到分钟
        handlers=[
            logging.FileHandler(log_filename, mode='a', encoding='utf-8'),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"
    # tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/test1.shp"

    gdf = gpd.read_file(tile_def)

    # for i, prop in gdf.iterrows():
    #     processRow((i, prop), paras=paras, out_path=out_path)
    # # processRow((0,gdf.iloc[0]), paras=(im), out_path=out_path)

    p = Pool(20)
    p.imap(partial(processRow, paras=paras, out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt cls_2023.vrt cls*.tif'
    os.system(cmd)
    cmd = 'gdalbuildvrt patch_2023.vrt patch*.tif'
    os.system(cmd)