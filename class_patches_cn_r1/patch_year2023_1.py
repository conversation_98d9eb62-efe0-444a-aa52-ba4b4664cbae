
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters


def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):

    base2023_cls, base2020_cls, new2020_cls = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    oname = path.join(out_path, name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    with rio.open(new2020_cls) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['dtype'] = 'uint16'
        meta['OVERVIEW_RESAMPLING'] = 'NEAREST'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    # union_recls_array = np.zeros_like(cls_array)
    new2020_cls_array = get_tiled_imarray(new2020_cls, xmin, ymin, xmax, ymax)[0]
    base2023_cls_array = get_tiled_imarray(base2023_cls, xmin, ymin, xmax, ymax)[0]
    base2020_cls_array = get_tiled_imarray(base2020_cls, xmin, ymin, xmax, ymax)[0]

    changemask = base2020_cls_array != base2023_cls_array
    new2020_cls_array[changemask] = base2023_cls_array[changemask]

    if new2020_cls_array.max() < 1:
        return
    meta['height'], meta['width'] = new2020_cls_array.shape
    
    with rio.open(oname,'w',**meta) as dst:
        dst.write(new2020_cls_array,1)




if __name__ =='__main__':
    # update_ndvi = '/mnt/mfs1/classT/0.DataCollection/composits/ChinaYearlyNoSnow_indexes_p85/index85_hh.vrt'
    # ccd_path = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'

    new2020_cls = '/mnt/mfs1/class2/0.ecomap/0.process/stacks/2020a1bc.tif'
    base2023_cls = '/mnt/Netapp/longtf/!eco.system/landcover/lc_hh_2023.tif'
    base2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/lc_hh_2020.tif'
    # ref2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt'

    # allow_src_dict = {
    #     'is':[107, 201, 203, 204, 302],
    #     'pv':[201, 203, 204, 302],
    # }

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/patchyear2023_1'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(base2023_cls, base2020_cls, new2020_cls), out_path=out_path)

    p = Pool(56)
    p.imap(partial(processRow, paras=(base2023_cls, base2020_cls, new2020_cls), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt 2023a1bc.vrt *.tif'
    os.system(cmd)