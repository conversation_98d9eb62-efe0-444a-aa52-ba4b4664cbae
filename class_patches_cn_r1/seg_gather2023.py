
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime
from sklearn.neighbors import KNeighborsClassifier
import rasterstats
from rasterstats import zonal_stats
import json
import gc

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def set_logger(out_path):
    log_filename = path.join(out_path, f"log_{datetime.now().strftime('%Y%m%d')}.log")  # 日志文件按日期命名
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
        datefmt="%Y-%m-%d %H:%M",  # 日期格式精确到分钟
        handlers=[
            logging.FileHandler(log_filename, mode='a', encoding='utf-8'),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

# 定义一个函数来创建环形缓冲区
def create_buffer(geometry, buffer_distance):
    """
    创建环形缓冲区（去掉原始多边形的缓冲区部分）。
    :param geometry: 输入的 shapely 多边形对象
    :param buffer_distance: 缓冲距离
    :return: 环形多边形
    """
    # 生成缓冲区
    buffered = geometry.buffer(buffer_distance)

    return buffered

def processRow(row, paras, out_path):
    # GRASS_ID = [201,202,203,204,205]
    pid = os.getpid()
    
    new23, new20, ccd, todomask, cisc22, update_pv, valid2022 = paras
    shpname = row
    gdf = gpd.read_file(shpname)
    xmin, ymin, xmax, ymax = gdf.total_bounds
    name = path.basename(shpname)
    logging.info(f"Process {pid} is starting {name}")

    oname_cls = path.join(out_path, name)
    # oname_patch = path.join(out_path, 'patch_'+name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname_cls):
        return
    
    with rio.open(new20) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['OVERVIEW_RESAMPLING']='NEAREST'

    e = (xmin, ymin, xmax, ymax)
    cls20_array = get_tiled_imarray(new20, xmin, ymin, xmax, ymax)[0]
    cls23_array = get_tiled_imarray(new23, xmin, ymin, xmax, ymax)[0]
    unchange_array = cls23_array.copy()
    unchange_array[cls20_array!=cls23_array] = 0
    changed_array = cls23_array.copy()
    changed_array[cls20_array==cls23_array] = 0
    # if cls_array.max() < 1:
    #     return
    results_z = []
    results_area = []
    result_neighber = []
    for i, p in enumerate(gdf.geometry):
        # 以0.001概率输出日志
        if np.random.rand() < 0.001:
            logging.info(f"Process {pid} is processing {name} {i}/{len(gdf)}")
        # 统计需要体现：1. 不变（变）的数量cls20！=cls23；2. 变化区域的主要类型（各类数量）；3. 变化区域邻域的主要类型
        p_buffer = create_buffer(p, 120)
        sta_z = zonal_stats(p, changed_array, categorical=True, affine=transform_local)[0]
        area = np.sum(list(sta_z.values()))
        sta_neighber = zonal_stats(p_buffer, unchange_array, categorical=True, affine=transform_local)[0]
        results_z.append(json.dumps(sta_z))
        results_area.append(area)
        result_neighber.append(json.dumps(sta_neighber))
        del p_buffer, sta_z, sta_neighber

    gdf['changed'] = results_z
    gdf['area'] = results_area
    gdf['neighber'] = result_neighber

    gdf.to_file(oname_cls)
    logging.info(f"Process {pid} finished {name}")
    del gdf
    gc.collect()
    return




if __name__ =='__main__':
    valid2022 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/valid2m/valid2m.vrt'
    # valid2020 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/valid2m_2020/valid2m.vrt'
    # update_cisc2020 = '/mnt/mfs1/classT/0.Dataset/CISC_2020/isc_hh.vrt'
    # update_cisc2022 = '/mnt/mfs1/classT/0.Dataset/CISC_2022/isc2022_cn_ecolc.vrt'
    update_pv = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/pv_change/pv_change.vrt'

    # base_cls = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt'
    # ref2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt'

    # prob20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2020_hh_prob_1/prob2020.vrt'
    # prob23 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2023_hh_prob_1/prob2023.vrt'

    ccd = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    # update_ndvi = '/mnt/mfs1/classT/0.DataCollection/composits/ChinaYearlyNoSnow_indexes_p85/index85_hh.vrt'


    # initpatch_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patch302_clean_1230/patch302.vrt'
    # base10 = '/mnt/Netapp/longtf/!eco.system/landcover/base/2010lc_30m_type2015.tif'
    # base20 = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.tif'
    # base20 = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt'
    new20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patches_2020_fixISdrop/cls_2020.vrt'
    new23 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patches_fixdrop_2023/cls_2023.vrt'
    cisc22 = '/mnt/mfs1/classT/0.Dataset/CISC_2022/isc2022_cn_ecolc.vrt'
    todomask = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/2023todo_mask.tif'
    # shade_path = '/mnt/mfs/TRANS/0.eco_shade/shade.vrt'

    ee_2023 = '/mnt/mfs/TRANS/longtf/lc_2023/index.vrt'


    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/seg_gather_2023'
    paras = (new23, new20, ccd, todomask, cisc22, update_pv, valid2022)
    if not path.exists(out_path):
        makedirs(out_path)

    set_logger(out_path)


    seg_dir = "/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/cn_2023_seg"
    shps = glob(path.join(seg_dir, '*change.shp'))


    # for s in shps:
    #     processRow(s, paras=paras, out_path=out_path)
    # # processRow((0,gdf.iloc[0]), paras=(im), out_path=out_path)

    p = Pool(6)
    p.imap(partial(processRow, paras=paras, out_path=out_path), shps)
    p.close()
    p.join()
    # os.chdir(out_path)
    # cmd = 'gdalbuildvrt cls_2023.vrt cls*.tif'
    # os.system(cmd)
    # cmd = 'gdalbuildvrt patch_2023.vrt patch*.tif'
    # os.system(cmd)