import geopandas as gpd
from shapely.geometry import Polygon

# 定义一个函数来创建环形缓冲区
def create_annular_buffer(geometry, buffer_distance):
    """
    创建环形缓冲区（去掉原始多边形的缓冲区部分）。
    :param geometry: 输入的 shapely 多边形对象
    :param buffer_distance: 缓冲距离
    :return: 环形多边形
    """
    # 生成缓冲区
    buffered = geometry.buffer(buffer_distance)
    # 差集计算（去掉原始多边形部分）
    annular_buffer = buffered.difference(geometry)
    return annular_buffer

# 示例：对 shapefile 数据进行操作
def process_shapefile(shp_path, output_path, buffer_distance):
    """
    对 shapefile 数据生成环形缓冲区。
    :param shp_path: 输入 shapefile 路径
    :param output_path: 输出 shapefile 路径
    :param buffer_distance: 缓冲距离
    """
    # 读取 shapefile 数据
    gdf = gpd.read_file(shp_path)
    
    # 创建环形缓冲区
    gdf['geometry'] = gdf['geometry'].apply(lambda geom: create_annular_buffer(geom, buffer_distance))
    
    # 保存结果
    gdf.to_file(output_path, driver="ESRI Shapefile")

# 示例调用
if __name__ == "__main__":
    input_shp = "/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/cn_2023_seg/N22E24_change.shp"
    output_shp = "/mnt/mfs1/class2/0.ecomap/0.process/test_buffer.shp"
    buffer_distance = 60  # 缓冲距离（单位取决于数据的 CRS）
    
    process_shapefile(input_shp, output_shp, buffer_distance)
