
import rasterio as rio
from rasterio.transform import xy
from rasterio.warp import transform as transform_crs
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
import geopandas as gpd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from shapely.geometry import Point

ECO_MAP = {
    1: [101, 102, 103, 104, 105, 109],
    2: [106, 107, 108, 110],
    3: [201,202,203,204],
    4: [401, 402, 403, 404, 405, 406, 407],
    5: [111, 112, 301, 302],
    6: [113, 114, 205, 501, 502, 503, 504],
    7: [604],
    8: [601, 602, 603, 605, 606]
}

CLIST = []
for k, v in ECO_MAP.items():
    CLIST.extend(v)

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    vrt_cls = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    n_samples = 50

    oname = path.join(out_path, 'sample_' + name + '.shp')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    transform_local = None
    all_geometry = []
    all_id = []
    cls_array = get_tiled_imarray(vrt_cls, xmin, ymin, xmax, ymax)[0]
    local_clist = np.unique(cls_array)
    for k in local_clist:
        if transform_local is None:
            with rio.open(vrt_cls) as src:
                transform = src.transform
                row_start, col_start = src.index(xmin, ymax)
                transform_local = transform * rio.Affine.translation(col_start, row_start)
                crs = src.crs
        if cls_array.max()<1:
            continue
        ones_positions = np.argwhere(cls_array == k)
        if len(ones_positions) >= n_samples:
            selected_indices = np.random.choice(len(ones_positions), n_samples, replace=False)
            selected_positions = ones_positions[selected_indices]
        else:
            selected_positions = ones_positions
        xs, ys = xy(transform_local, selected_positions[:, 0], selected_positions[:, 1])
        longs, lats = transform_crs(crs, 'epsg:4326', xs, ys)
        geometry = [Point(xy) for xy in zip(longs, lats)]
        all_geometry.extend(geometry)
        all_id.extend([k]*len(geometry))
    gdf = gpd.GeoDataFrame(data={'rs_code': all_id}, geometry=all_geometry, crs="EPSG:4326")
    if len(gdf) == 0:
        return
    gdf.to_file(oname)





if __name__ =='__main__':
    new20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn_r1/patches_2020_fixISdrop/cls_2020.vrt'

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn_r1/ValSamples'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(new20), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(new20), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    # os.chdir(out_path)
    # cmd = 'gdalbuildvrt intersect302.vrt *.tif'
    # os.system(cmd)