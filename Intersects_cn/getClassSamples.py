
import rasterio as rio
from rasterio.transform import xy
from rasterio.warp import transform as transform_crs
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
import geopandas as gpd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from shapely.geometry import Point

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    cls_dict = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    n_samples = 50

    oname = path.join(out_path, 'sample_' + name + '.shp')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    transform_local = None
    all_geometry = []
    all_id = []
    for k, v in cls_dict.items():
        if transform_local is None:
            with rio.open(v) as src:
                transform = src.transform
                row_start, col_start = src.index(xmin, ymax)
                transform_local = transform * rio.Affine.translation(col_start, row_start)
                crs = src.crs
        cls_array = get_tiled_imarray(v, xmin, ymin, xmax, ymax)[0]
        if cls_array.max()<1:
            continue
        ones_positions = np.argwhere(cls_array == 1)
        if len(ones_positions) >= n_samples:
            selected_indices = np.random.choice(len(ones_positions), n_samples, replace=False)
            selected_positions = ones_positions[selected_indices]
        else:
            selected_positions = ones_positions
        xs, ys = xy(transform_local, selected_positions[:, 0], selected_positions[:, 1])
        longs, lats = transform_crs(crs, 'epsg:4326', xs, ys)
        geometry = [Point(xy) for xy in zip(longs, lats)]
        all_geometry.extend(geometry)
        all_id.extend([k]*len(geometry))
    gdf = gpd.GeoDataFrame(data={'rs_code': all_id}, geometry=all_geometry, crs="EPSG:4326")
    if len(gdf) == 0:
        return
    gdf.to_file(oname)





if __name__ =='__main__':
    cls_dict = {
        # 101:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect101/intersect101.vrt',
        # 102:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect102/intersect102.vrt',
        # 103:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect103/intersect103.vrt',
        # 107:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect107/intersect107.vrt',
        # 201:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect201/intersect201.vrt',
        # 203:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect203/intersect203.vrt',
        # 204:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect204/intersect204.vrt',
        301:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect301/intersect301.vrt',
        302:'/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect302/intersect302.vrt',
    }

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/modCropSampls_cn_30x'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(cls_dict), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(cls_dict), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    # os.chdir(out_path)
    # cmd = 'gdalbuildvrt intersect302.vrt *.tif'
    # os.system(cmd)