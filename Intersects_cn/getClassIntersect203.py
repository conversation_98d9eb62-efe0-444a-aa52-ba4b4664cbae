
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    cls_path_list, cls_map_list = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    encode_oname = path.join(out_path, 'intersect_' + name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(encode_oname):
        return
    
    with rio.open(cls_path_list[0]) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    # union_recls_array = np.zeros_like(cls_array)
    
    for i in range(len(cls_path_list)):
        cls_array = get_tiled_imarray(cls_path_list[i], xmin, ymin, xmax, ymax)[0]
        if i ==0:
            intersect_recls_array = np.ones_like(cls_array)
        
        intersect_recls_array[~np.isin(cls_array, cls_map_list[i])] = 0

    if intersect_recls_array.max() < 1:
        return
    meta['height'], meta['width'] = intersect_recls_array.shape
    
    with rio.open(encode_oname,'w',**meta) as dst:
        dst.write(intersect_recls_array,1)




if __name__ =='__main__':
    # raster_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # raster_ref_path = '/mnt/mfs/TRANS/longtf/landsat_2020/2020.vrt'
    # change_mask_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # shp_path = '/mnt/Netapp/longtf/!eco.system/changes/cn_2023_yellow_river/N14E15_change.shp'
    # mask_path = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    # cls_path = '/mnt/Netapp/longtf/!eco.system/landcover/2010lc_30m_type2015.tif'
    cls_path_list = [
        '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt',
        '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt',
        # '/mnt/Netapp/yinry/0.LCLU.DATA/GFSAD30/GFSAD30AUNZCNMOCE.001/albers.vrt',
        '/mnt/Netapp/yinry/0.LCLU.DATA/GLC.Zhangxiao/GLC_FCS30-2020/ChinaTileAlbers.vrt',
        # '/mnt/Netapp/yinry/0.LCLU.DATA/Global_cropland_GLAD/Global_cropland_NE_2019_albers.vrt',
        '/mnt/Netapp/yinry/0.LCLU.DATA/CLCD1.0.1/CLCD_v01_2020_albert_new.vrt',
        # '/mnt/Netapp/yinry/0.LCLU.DATA/CACD/CACD-2020_albers.vrt',
        '/mnt/Netapp/yinry/0.LCLU.DATA/ESA_WorldCover_10m_2020_v100_Map/tiles_rect_china_albers.vrt'

        ]
    cls_map_list = [
        [203], 
        [203],
        # [2],
        [130],
        # [1],
        [4],
        # [1],
        [30]
    ]

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/cn_intersect203'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(cls_path_list, cls_map_list), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(cls_path_list, cls_map_list), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt intersect203.vrt *.tif'
    os.system(cmd)