﻿# coding: utf-8
# full width versions (SPACE is non-contiguous with ! through ~)
SPACE = '\N{IDEOGRAPHIC SPACE}'
EXCLA = '\N{FULLWIDTH EXCLAMATION MARK}'
TILDE = '\N{FULLWIDTH TILDE}'

# strings of ASCII and full-width characters (same order)
west = ''.join(chr(i) for i in range(ord(' '), ord('~')))
east = SPACE + ''.join(chr(i) for i in range(ord(EXCLA), ord(TILDE)))

# build the translation table
full = str.maketrans(west, east)

import sys
import os
import re
from itertools import groupby
import string
import ntpath
import glob
import re
import multiprocessing
from tqdm import tqdm
from collections import namedtuple
import numpy as np
import argparse
from numpy import ma
from kitchen.text.display import *
from multiprocessing import Process, Manager, Lock
from multiprocessing import Pool
import pandas as pd
from tabulate import tabulate

try:
    from osgeo import gdal
    from osgeo import ogr
    from osgeo import osr
    # from osgeo.gdalnumeric import *
    from osgeo.gdalconst import *
except ImportError:
    print('You need GDAL installed')
    sys.exit(-1)


def path_transform(in_path):
    in_path = in_path.replace("\\", "/")
    out_path = in_path
    if in_path.find(":") >= 0 and not os.name == "nt":
        # windows path for non windows system
        out_path = in_path.replace("Z:/", "/mnt/Netapp/")
        out_path = out_path.replace("V:/", "/mnt/mfs1/")
        out_path = out_path.replace("U:/", "/mnt/mfs/")
        out_path = out_path.replace("P:/", "/mnt/data20/")
    elif in_path.find(":") < 0 and os.name == "nt":
        # linux path for windows system
        out_path = in_path.replace("/mnt/Netapp/", "Z:/")
        out_path = out_path.replace("/mnt/mfs/", "U:/")
        out_path = out_path.replace("/mnt/mfs1/", "V:/")
        out_path = out_path.replace("/mnt/data20/", "P:/")
    return out_path


Block = namedtuple('Block', ['x_offset', 'y_offset', 'width', 'height',
                             'padding_left', 'padding_top', 'padding_right',
                             'padding_bottom'])

class_name = {
    '0': u'填充',
    '101': u'常绿阔叶林',
    '102': u'落叶阔叶林',
    '103': u'常绿针叶林',
    '104': u'落叶针叶林',
    '105': u'针阔混交林',
    '106': u'常绿阔叶灌木林',
    '107': u'落叶阔叶灌木林',
    '108': u'常绿针叶灌木林',
    '109': u'稀疏林',
    '110': u'稀疏灌木林',
    '111': u'乔木园地',
    '112': u'灌木园地',
    '113': u'乔木绿地',
    '114': u'灌木绿地',
    '201': u'草原',
    '202': u'草甸',
    '203': u'草丛',
    '204': u'稀疏草地',
    '205': u'草本绿地',
    '301': u'水田',
    '302': u'旱地',
    '401': u'乔木湿地',
    '402': u'灌木湿地',
    '403': u'草本湿地',
    '404': u'湖泊',
    '405': u'水库/坑塘',
    '406': u'河流',
    '407': u'运河/水渠',
    '501': u'居住地',
    '502': u'工业用地',
    '503': u'交通用地',
    '504': u'采矿场',
    '601': u'苔藓/地衣',
    '602': u'裸岩',
    '603': u'裸土',
    '604': u'沙漠',
    '605': u'盐碱地',
    '606': u'冰川/永久积雪',
}

cls_name_dict_eco_l1 = {
    '0': u'填充',
    '1': u'森林生态系统',
    '2': u'灌丛生态系统',
    '3': u'草地生态系统',
    '4': u'湿地生态系统',
    '5': u'农田生态系统',
    '6': u'城镇生态系统',
    '7': u'荒漠生态系统',
    '8': u'冰川/永久积雪',
}

begin_class_count_map = {}
end_class_count_map = {}
change_class_count_map = {}


def find_all_files(in_folder, pat=r'.txt$'):
    file_list = []
    for root, _, files in os.walk(in_folder):
        for file in files:
            match = re.search(pat, file, re.I)
            if match:
                file_list.append(os.path.join(root, file))
    return file_list


def change_tile(ds1, ds2, block):
    nodata_value1 = ds1.GetRasterBand(1).GetNoDataValue()
    nodata_value2 = ds2.GetRasterBand(1).GetNoDataValue()

    samp_offset = block.x_offset - block.padding_left
    nSamps = block.width + block.padding_left + block.padding_right
    line_offset = block.y_offset - block.padding_top
    nLines = block.height + block.padding_top + block.padding_bottom

    block_data1 = ds1.GetRasterBand(1).ReadAsArray(samp_offset, line_offset, \
                                                   nSamps, nLines)
    block_data2 = ds2.GetRasterBand(1).ReadAsArray(samp_offset, line_offset, \
                                                   nSamps, nLines)

    class_list1 = list(np.unique(block_data1))
    class_list2 = list(np.unique(block_data2))
    if nodata_value1 in class_list1:
        class_list1.remove(int(nodata_value1))
    if nodata_value2 in class_list2:
        class_list2.remove(int(nodata_value2))

    if not class_list1 and not class_list2:
        return

    d1_list = {}
    d2_list = {}
    for class1 in class_list1:
        d1_list[class1] = block_data1 == class1
        c1 = np.sum(d1_list[class1])
        if c1 > 0:
            class_key = '{}'.format(class1)
            if class_key in begin_class_count_map:
                begin_class_count_map[class_key] += c1
            else:
                begin_class_count_map[class_key] = c1
    for class2 in class_list2:
        d2_list[class2] = block_data2 == class2
        c2 = np.sum(d2_list[class2])
        if c2 > 0:
            class_key = '{}'.format(class2)
            if class_key in end_class_count_map:
                end_class_count_map[class_key] += c2
            else:
                end_class_count_map[class_key] = c2

    for class1 in class_list1:
        for class2 in class_list2:
            d = np.logical_and(d1_list[class1], d2_list[class2])
            c = np.sum(d)
            if c > 0:
                change_key = '{}-{}'.format(class1, class2)
                if change_key in change_class_count_map:
                    change_class_count_map[change_key] += c
                else:
                    change_class_count_map[change_key] = 0


def change_statistics(file1, file2, out_file):
    global begin_class_count_map
    global end_class_count_map
    global change_class_count_map

    begin_class_count_map = {}
    end_class_count_map = {}
    change_class_count_map = {}

    ds1 = gdal.Open(file1, GA_ReadOnly)
    nWidth1, nHeight1 = ds1.RasterXSize, ds1.RasterYSize
    ds2 = gdal.Open(file2, GA_ReadOnly)
    nWidth2, nHeight2 = ds2.RasterXSize, ds2.RasterYSize

    assert (nWidth1 == nWidth2 and nHeight1 == nHeight2)

    nWidth = nWidth1
    nHeight = nHeight1

    gt = ds1.GetGeoTransform()
    cell_area = gt[1] * (-gt[5]) * 1e-6  # km2

    block_list = []
    iBlockSize = 1024
    padding_size = 0
    nBlocksX = int(np.ceil(nWidth / float(iBlockSize)))
    nBlocksY = int(np.ceil(nHeight / float(iBlockSize)))
    for iBlockX in range(nBlocksX):
        samp_offset = iBlockX * iBlockSize
        nSamps = min(iBlockSize, nWidth - samp_offset)
        padding_left = min(padding_size, samp_offset - 0)
        padding_right = min(padding_size, nWidth - samp_offset - nSamps)
        for iBlockY in range(nBlocksY):
            line_offset = iBlockY * iBlockSize
            nLines = min(iBlockSize, nHeight - line_offset)
            padding_top = min(padding_size, line_offset - 0)
            padding_bottom = min(padding_size, nHeight - line_offset - nLines)
            block_list.append(Block(x_offset=samp_offset, y_offset=line_offset, width=nSamps, height=nLines,
                                    padding_left=padding_left, padding_right=padding_right, padding_top=padding_top,
                                    padding_bottom=padding_bottom))

    for block in tqdm(block_list):
        change_tile(ds1, ds2, block)

    # write results
    out_folder = os.path.dirname(out_file)
    if not os.path.exists(out_folder):
        os.makedirs(out_folder)

    change_keys = change_class_count_map.keys()
    class_list1 = [k.split('-')[0] for k in change_keys]
    class_list2 = [k.split('-')[1] for k in change_keys]
    class_list1 = np.unique(class_list1)
    class_list2 = np.unique(class_list2)
    class_string2 = '%10s' % ('')
    for cls in class_list2:
        class_string2 += '%10s' % (cls)
    with open(out_file, 'w', encoding='utf-8-sig') as h_file:
        h_file.write('---------------------- transition matrix (unit: pixel) ----------------------\n')
        h_file.write(class_string2 + '\n')
        for cls1 in class_list1:
            h_file.write('%10s' % (cls1))
            for cls2 in class_list2:
                change_key = '{}-{}'.format(cls1, cls2)
                if change_key in change_class_count_map:
                    h_file.write('%10d' % change_class_count_map[change_key])
                else:
                    h_file.write('%10d' % 0)
            h_file.write('\n')

        #
        totoal_class_list = np.unique(list(class_list1) + list(class_list2))
        h_file.write('\n')
        h_file.write('\n')
        # h_file.write('--------------- begin year ----------------\n')
        h_file.write('--------------------- change statisitcs (unit: km^2) ----------------------\n')
        h_file.write(u'%s%15s%15s%15s%15s\n' % (
            textual_width_fill(u'类型', 15, 15, left=False), 'begin_year', 'end_year', 'change', 'rate(%)'))
        for i in range(75):
            h_file.write('-')
        h_file.write('\n')
        for cls in totoal_class_list:
            h_file.write(u'%s' % (textual_width_fill(class_name[cls], 15, 15, left=False)))
            # h_file.write(u'%20s' %(cls))
            begin_count = 0
            if cls in class_list1:
                begin_count = begin_class_count_map[cls] * cell_area
            end_count = 0
            if cls in class_list2:
                end_count = end_class_count_map[cls] * cell_area
            change = end_count - begin_count
            change_rate = 1
            if begin_count != 0:
                change_rate = change * 100. / begin_count
            h_file.write(u'%15.2f%15.2f%15.2f%15.2f\n' % (begin_count, end_count, change, change_rate))
        for i in range(75):
            h_file.write('-')
        h_file.write('\n')


def mp_change_tile(args):
    file1, file2, block, cls_map_dict = args['file1'], args['file2'], args['block'], args['cls_map_dict']

    ds1 = gdal.Open(file1, gdal.GA_ReadOnly)
    ds2 = gdal.Open(file2, gdal.GA_ReadOnly)

    nodata_value1 = ds1.GetRasterBand(1).GetNoDataValue()
    nodata_value2 = ds2.GetRasterBand(1).GetNoDataValue()
    nodata_value1 = 0
    nodata_value2 = 0

    samp_offset = block.x_offset - block.padding_left
    nSamps = block.width + block.padding_left + block.padding_right
    line_offset = block.y_offset - block.padding_top
    nLines = block.height + block.padding_top + block.padding_bottom

    block_data1 = ds1.GetRasterBand(1).ReadAsArray(samp_offset, line_offset, nSamps, nLines)
    block_data2 = ds2.GetRasterBand(1).ReadAsArray(samp_offset, line_offset, nSamps, nLines)

    msk1 = block_data1 != nodata_value1
    msk2 = block_data2 != nodata_value2
    msk = msk1 & msk2
    block_data1 = np.ma.masked_array(block_data1, mask=~msk)
    block_data2 = np.ma.masked_array(block_data2, mask=~msk)

    if cls_map_dict is not None:
        block_data1 = np.vectorize(lambda x: cls_map_dict.get(x, x))(block_data1)
        block_data2 = np.vectorize(lambda x: cls_map_dict.get(x, x))(block_data2)

    class_list1 = np.unique(block_data1)
    class_list2 = np.unique(block_data2)
    class_list1 = class_list1[class_list1 != nodata_value1]
    class_list2 = class_list2[class_list2 != nodata_value2]

    if not class_list1.size and not class_list2.size:
        return

    local_begin_count = {}
    local_end_count = {}
    local_change_count = {}

    for class1 in class_list1:
        c1 = np.sum(block_data1 == class1)
        if c1 > 0:
            local_begin_count[str(class1)] = c1

    for class2 in class_list2:
        c2 = np.sum(block_data2 == class2)
        if c2 > 0:
            local_end_count[str(class2)] = c2

    for class1 in class_list1:
        for class2 in class_list2:
            c = np.sum((block_data1 == class1) & (block_data2 == class2))
            if c > 0:
                local_change_count[f'{class1}-{class2}'] = c

    # print(local_begin_count, local_end_count, local_change_count)
    return local_begin_count, local_end_count, local_change_count


note_row = 2


def get_extent(ds):
    """Get the extent of a GDAL dataset."""
    gt = ds.GetGeoTransform()
    minx = gt[0]
    maxx = gt[0] + (ds.RasterXSize * gt[1])
    miny = gt[3] + (ds.RasterYSize * gt[5])
    maxy = gt[3]
    return (minx, miny, maxx, maxy)


def intersect_extent(extent1, extent2):
    """Calculate the intersection of two extents."""
    minx = max(extent1[0], extent2[0])
    miny = max(extent1[1], extent2[1])
    maxx = min(extent1[2], extent2[2])
    maxy = min(extent1[3], extent2[3])

    if minx < maxx and miny < maxy:
        return (minx, miny, maxx, maxy)
    else:
        return None  # No intersection


def change_statistics_parallel(file1, file2, out_file, n_process=4, cls_map_dict_csv_file='', cls_name_dict=class_name):
    file1 = path_transform(file1)
    file2 = path_transform(file2)
    out_file = path_transform(out_file)
    cls_map_dict_csv_file = path_transform(cls_map_dict_csv_file)
    out_csv_file = out_file.replace('.txt', '.xlsx')

    cls_map_dict = None
    if os.path.isfile(cls_map_dict_csv_file):
        rs_df = pd.read_csv(cls_map_dict_csv_file)
        cls_map_dict = dict(zip(rs_df['from'].values, rs_df['to'].values))

    global note_row

    def get_note_row():
        global note_row
        note_row += 1
        return note_row

    ds1_ = gdal.Open(file1, gdal.GA_ReadOnly)
    nWidth, nHeight = ds1_.RasterXSize, ds1_.RasterYSize

    ds2_ = gdal.Open(file2, gdal.GA_ReadOnly)
    # assert (nWidth == ds2_.RasterXSize and nHeight == ds2_.RasterYSize)

    # Get extents
    extent1 = get_extent(ds1_)
    extent2 = get_extent(ds2_)

    # Calculate intersection
    intersection = intersect_extent(extent1, extent2)

    assert intersection is not None

    minx, miny, maxx, maxy = intersection

    tmp_vrt_file1 = os.path.join(os.path.dirname(out_file), 'tmp_file1.vrt')
    tmp_vrt_file2 = os.path.join(os.path.dirname(out_file), 'tmp_file2.vrt')

    # Create a new VRT file in memory with the same extent as the geometry
    options = gdal.TranslateOptions(format='VRT', projWin=[minx, maxy, maxx, miny])
    ds1 = gdal.Translate(tmp_vrt_file1, ds1_, options=options)
    ds2 = gdal.Translate(tmp_vrt_file2, ds2_, options=options)
    nWidth, nHeight = ds1.RasterXSize, ds1.RasterYSize

    gt = ds1.GetGeoTransform()
    cell_area = gt[1] * (-gt[5]) * 1e-6  # km2

    iBlockSize = 1024
    padding_size = 0
    nBlocksX = int(np.ceil(nWidth / float(iBlockSize)))
    nBlocksY = int(np.ceil(nHeight / float(iBlockSize)))

    block_list = [
        Block(
            x_offset=iBlockX * iBlockSize,
            y_offset=iBlockY * iBlockSize,
            width=min(iBlockSize, nWidth - iBlockX * iBlockSize),
            height=min(iBlockSize, nHeight - iBlockY * iBlockSize),
            padding_left=min(padding_size, iBlockX * iBlockSize),
            padding_right=min(padding_size,
                              nWidth - (iBlockX * iBlockSize + min(iBlockSize, nWidth - iBlockX * iBlockSize))),
            padding_top=min(padding_size, iBlockY * iBlockSize),
            padding_bottom=min(padding_size,
                               nHeight - (iBlockY * iBlockSize + min(iBlockSize, nHeight - iBlockY * iBlockSize)))
        )
        for iBlockY in range(nBlocksY)
        for iBlockX in range(nBlocksX)
    ]

    args_list = [{'file1': tmp_vrt_file1, 'file2': tmp_vrt_file2, 'block': block, 'cls_map_dict': cls_map_dict} for
                 block in block_list]

    with Pool(processes=n_process) as pool:
        results = list(tqdm(pool.imap(mp_change_tile, args_list), total=len(args_list)))

    mp_begin_class_count_map = {}
    mp_end_class_count_map = {}
    mp_change_class_count_map = {}

    for r in results:
        if r is None:
            continue
        local_begin, local_end, local_change = r
        for k, v in local_begin.items():
            mp_begin_class_count_map[k] = mp_begin_class_count_map.get(k, 0) + v
        for k, v in local_end.items():
            mp_end_class_count_map[k] = mp_end_class_count_map.get(k, 0) + v
        for k, v in local_change.items():
            mp_change_class_count_map[k] = mp_change_class_count_map.get(k, 0) + v

    # Write results
    os.makedirs(os.path.dirname(out_file), exist_ok=True)

    change_keys = mp_change_class_count_map.keys()
    class_list1 = sorted(set(k.split('-')[0] for k in change_keys))
    class_list2 = sorted(set(k.split('-')[1] for k in change_keys))

    with open(out_file, 'w', encoding='utf-8-sig') as h_file:
        h_file.write('---------------------- Transition Matrix (unit: pixel) ----------------------\n\n')

        # Create the transition matrix
        matrix_data = {cls2: [mp_change_class_count_map.get(f'{cls1}-{cls2}', 0) for cls1 in class_list1]
                       for cls2 in class_list2}
        df = pd.DataFrame(matrix_data, index=class_list1)

        # Add row and column totals
        df['Row Total'] = df.sum(axis=1)
        df.loc['Column Total'] = df.sum()

        # Calculate percentages
        total_pixels = df.loc['Column Total', 'Row Total']

        # Use apply instead of applymap
        df_percentage = df.apply(lambda col: col.map(lambda x: f'{x} ({x / total_pixels:.2%})' if x != 0 else ''))

        # Replace class codes with names and add class IDs
        df_percentage.index = [f"{cls_name_dict.get(cls, cls)} [{cls}]" for cls in df_percentage.index]
        df_percentage.columns = [f"{cls_name_dict.get(cls, cls)} [{cls}]" for cls in df_percentage.columns]

        # Format the table
        table = tabulate(df_percentage, headers='keys', tablefmt='pretty', showindex=True, numalign='right')
        h_file.write(table)

        h_file.write('\n\nNote: Each cell shows: count (percentage of total)\n')
        h_file.write('Row totals show the pixel count for each class in the beginning year.\n')
        h_file.write('Column totals show the pixel count for each class in the ending year.\n')

        # Add summary statistics
        h_file.write('\n\nSummary Statistics:\n')
        h_file.write(f'Total pixels: {total_pixels:,}\n')
        unchanged_pixels = sum(
            [mp_change_class_count_map.get(f'{cls}-{cls}', 0) for cls in set(class_list1) & set(class_list2)])
        h_file.write(f'Unchanged pixels: {unchanged_pixels:,} ({unchanged_pixels / total_pixels:.2%})\n')
        changed_pixels = total_pixels - unchanged_pixels
        h_file.write(f'Changed pixels: {changed_pixels:,} ({changed_pixels / total_pixels:.2%})\n')

        # Detailed transition statistics
        h_file.write('\n\n---------------------- Detailed Transition Statistics ----------------------\n\n')

        # Calculate total area for each class in the beginning year
        begin_class_areas = {
            cls: sum([mp_change_class_count_map.get(f'{cls}-{cls2}', 0) for cls2 in class_list2]) * cell_area
            for cls in class_list1}

        transitions = []
        for cls1 in class_list1:
            for cls2 in class_list2:
                if cls1 != cls2:  # Exclude unchanged classes
                    pixel_count = mp_change_class_count_map.get(f'{cls1}-{cls2}', 0)
                    if pixel_count > 0:
                        area = pixel_count * cell_area
                        ratio = area / begin_class_areas[cls1] if begin_class_areas[cls1] > 0 else 0
                        transitions.append({
                            'From': f"{cls_name_dict.get(cls1, cls1)} [{cls1}]",
                            'To': f"{cls_name_dict.get(cls2, cls2)} [{cls2}]",
                            'Pixel Count': pixel_count,
                            'Area (km²)': area,
                            'Ratio': ratio
                        })

        # Sort transitions by area in descending order
        transitions_df = pd.DataFrame(transitions).sort_values('Area (km²)', ascending=False)

        # Format the dataframe
        transitions_df['Pixel Count'] = transitions_df['Pixel Count'].map('{:,}'.format)
        transitions_df['Area (km²)'] = transitions_df['Area (km²)'].map('{:.2f}'.format)
        transitions_df['Ratio'] = transitions_df['Ratio'].map('{:.2%}'.format)

        # Convert dataframe to a nicely formatted table
        table = tabulate(transitions_df, headers='keys', tablefmt='pretty', showindex=False, numalign='right')
        h_file.write(table)

        h_file.write('\n\nNote: Transitions are sorted by area in descending order. Unchanged classes are excluded.\n')
        h_file.write('Ratio represents the changed area compared to the original area of the "From" class.\n')

        h_file.write('\n\n--------------------- Change Statistics (unit: km²) ----------------------\n\n')
        total_class_list = sorted(set(class_list1 + class_list2))
        data = []
        for cls in total_class_list:
            begin_count = mp_begin_class_count_map.get(cls, 0) * cell_area
            end_count = mp_end_class_count_map.get(cls, 0) * cell_area
            change = end_count - begin_count
            change_rate = (change * 100 / begin_count) if begin_count != 0 else float('inf')

            data.append({
                'Class': cls_name_dict.get(cls, cls),
                'Begin Year': begin_count,
                'End Year': end_count,
                'Change': change,
                'Rate (%)': change_rate
            })

        df = pd.DataFrame(data)
        df['Change'] = df['Change'].abs()
        df = df.sort_values('Change', ascending=False)  # Sort by change magnitude
        df['Change'] = df['End Year'] - df['Begin Year']

        # Calculate totals
        totals = df.sum(numeric_only=True)
        totals['Class'] = 'Total'
        totals['Rate (%)'] = (totals['Change'] * 100 / totals['Begin Year']) if totals[
                                                                                    'Begin Year'] != 0 else float(
            'inf')
        df = pd.concat([df, pd.DataFrame([totals])], ignore_index=True)

        # Format the dataframe
        df['Begin Year'] = df['Begin Year'].map('{:,.2f}'.format)
        df['End Year'] = df['End Year'].map('{:,.2f}'.format)
        df['Change'] = df['Change'].map('{:+,.2f}'.format)
        df['Rate (%)'] = df['Rate (%)'].map('{:,.2f}'.format)

        # Convert dataframe to a nicely formatted table
        table = tabulate(df, headers='keys', tablefmt='pretty', showindex=False, numalign='right')
        h_file.write(table)

        h_file.write('\n\nNote: Positive change indicates an increase, negative indicates a decrease.\n')
        h_file.write('Classes are sorted by the magnitude of change.\n')

        ##########################################################################################################
        # Write excel file
        # Create a Pandas Excel writer using XlsxWriter as the engine.
        writer = pd.ExcelWriter(out_csv_file, engine='xlsxwriter')
        # Write your DataFrame to an excel sheet and name it.
        df.to_excel(writer, sheet_name='Change Statistics', index=False)

        workbook = writer.book
        worksheet = writer.sheets['Change Statistics']

        # Create a format for the header cells.
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            # 'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1,
            'font_name': 'Helvetica'})

        # Write the column headers with the defined format.
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)

        # Create a format for the data cells.
        data_format = workbook.add_format({
            'text_wrap': True,
            # 'valign': 'top',
            'border': 1,
            'font_name': 'Helvetica'})

        # Write data with the defined format.
        for row_num, row_data in enumerate(df.values):
            for col_num, col_data in enumerate(row_data):
                worksheet.write(row_num + 1, col_num, col_data, data_format)

        # Iterate over the columns and set each one to be as wide as the max length data entry.
        for i, col in enumerate(df.columns):
            # Find the maximum length of data in the column.
            column_len = df[col].astype(str).str.len().max()
            if i == 0:
                column_len *= 2
            # Get the max between the length of the column name and its max entry.
            column_len = max(column_len, len(col))
            # Add a little extra space for offset.
            column_len += 2
            # Set the width of the column to the max length of its entries.
            worksheet.set_column(i, i, column_len)

        text_format = workbook.add_format({
            # 'text_wrap': True,
            # 'border': 1,
            'font_name': 'Helvetica'})
        # Write additional text to the sheet without overwriting the DataFrame.
        note_row = 0
        worksheet.write_string(df.shape[0] + get_note_row(), 0, 'Change Statistics (unit: km²)', text_format)
        get_note_row()
        worksheet.write_string(df.shape[0] + get_note_row(), 0,
                               'Note: Positive change indicates an increase, negative indicates a decrease.',
                               text_format)
        worksheet.write_string(df.shape[0] + get_note_row(), 0, 'Classes are sorted by the magnitude of change.',
                               text_format)

        # Write the 'Detailed Transition Statistics' DataFrame to another excel sheet.
        transitions_df.to_excel(writer, sheet_name='Detailed Transition Statistics', index=False)
        workbook = writer.book
        worksheet = writer.sheets['Detailed Transition Statistics']

        # Write the column headers with the defined format.
        for col_num, value in enumerate(transitions_df.columns.values):
            worksheet.write(0, col_num, value, header_format)

        # Write data with the defined format.
        for row_num, row_data in enumerate(transitions_df.values):
            for col_num, col_data in enumerate(row_data):
                worksheet.write(row_num + 1, col_num, col_data, data_format)

        # Iterate over the columns and set each one to be as wide as the max length data entry.
        for i, col in enumerate(transitions_df.columns):
            # Find the maximum length of data in the column.
            column_len = transitions_df[col].astype(str).str.len().max()
            if i in [0, 1]:
                column_len *= 2
            # Get the max between the length of the column name and its max entry.
            column_len = max(column_len, len(col))
            # Add a little extra space for offset.
            column_len += 2
            # Set the width of the column to the max length of its entries.
            worksheet.set_column(i, i, column_len)
        note_row = 2
        worksheet.write_string(transitions_df.shape[0] + get_note_row(), 0,
                               'Note: Transitions are sorted by area in descending order. Unchanged classes are excluded.',
                               text_format)
        worksheet.write_string(transitions_df.shape[0] + get_note_row(), 0,
                               'Ratio represents the changed area compared to the original area of the "From" class.',
                               text_format)
        writer.close()


def stat_base():
    cls_map_dict_csv_file = r'Z:\longtf\!eco.system\landcover\convert\landcover2015toecol1.csv'
    change_statistics_parallel(r'Z:\longtf\!eco.system\landcover\base\2010lc_30m_type2015.tif',
                               r'Z:\longtf\!eco.system\landcover\base\2020lc_30m_type2015.tif',
                               r'Z:\longtf\!eco.system\changes\change_stat\2010-2020_ecol1_30m.txt', 100,
                               cls_map_dict_csv_file=cls_map_dict_csv_file,
                               cls_name_dict=cls_name_dict_eco_l1)
    change_statistics_parallel(r'Z:\longtf\!eco.system\landcover\base\2010lc_30m_type2015.tif',
                               r'Z:\longtf\!eco.system\landcover\base\2020lc_30m_type2015.tif',
                               r'Z:\longtf\!eco.system\changes\change_stat\2010-2020_ecol2_30m.txt', 100)


def stat_cn_result():
    cls_map_dict_csv_file = r'Z:\longtf\!eco.system\landcover\convert\landcover2015toecol1.csv'
    change_statistics_parallel(r'Z:\longtf\!eco.system\landcover\2010lc_30m_type2015.tif',
                               r'Z:\longtf\!eco.system\landcover\2020lc_30m_type2015.tif',
                               r'Z:\longtf\!eco.system\changes\change_stat\2010-2020_ecol1_30m.txt', 100,
                               cls_map_dict_csv_file=cls_map_dict_csv_file,
                               cls_name_dict=cls_name_dict_eco_l1)
    change_statistics_parallel(r'Z:\longtf\!eco.system\landcover\2010lc_30m_type2015.tif',
                               r'Z:\longtf\!eco.system\landcover\2020lc_30m_type2015.tif',
                               r'Z:\longtf\!eco.system\changes\change_stat\2010-2020_ecol2_30m.txt', 100)


def stat_hh_result():
    cls_map_dict_csv_file = r'Z:\longtf\!eco.system\landcover\convert\landcover2015toecol1.csv'
    # change_statistics_parallel(r"V:\class2\0.ecomap\0.process\stacks\2010a1b1c.tif",
    #                            r"V:\class2\0.ecomap\0.process\stacks\2020a1bc.tif",
    #                            r'V:\class2\0.ecomap\0.process\stacks\hh2010-2020_ecol1_30m.txt', 100,
    #                            cls_map_dict_csv_file=cls_map_dict_csv_file,
    #                            cls_name_dict=cls_name_dict_eco_l1)
    change_statistics_parallel(r"V:\class2\0.ecomap\0.process\stacks\2000a1b1c.tif",
                               r"V:\class2\0.ecomap\0.process\stacks\2010a1b1c.tif",
                            #    r"V:\class2\0.ecomap\0.process\stacks\2020a1bc.tif",
                               r'V:\class2\0.ecomap\0.process\stacks\hh2000-2010_ecol2_30m.txt', 100)

    # change_statistics_parallel(r"V:\class2\0.ecomap\0.process\stacks\2020a1bc.tif",
    #                            r'V:\class2\0.ecomap\0.process\stacks\2023a1bc.tif',
    #                            r'V:\class2\0.ecomap\0.process\stacks\hh2020-2023_ecol1_30m.txt', 100,
    #                            cls_map_dict_csv_file=cls_map_dict_csv_file,
    #                            cls_name_dict=cls_name_dict_eco_l1)
    # change_statistics_parallel(r"V:\class2\0.ecomap\0.process\stacks\2020a1bc.tif",
    #                            r'V:\class2\0.ecomap\0.process\stacks\2023a1bc.tif',
    #                            r'V:\class2\0.ecomap\0.process\stacks\hh2020-2023_ecol2_30m.txt', 100)


if __name__ == '__main__':
    # start from here
    gdal.UseExceptions()
    parser = argparse.ArgumentParser(description=' .')
    stat_hh_result()
