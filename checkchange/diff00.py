
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}
ECO_MAP = {
    1: [101, 102, 103, 104, 105, 109],
    2: [106, 107, 108, 110],
    3: [201,202,203,204],
    4: [401, 402, 403, 404, 405, 406, 407],
    5: [111, 112, 301, 302],
    6: [113, 114, 205, 501, 502, 503, 504],
    7: [604],
    8: [601, 602, 603, 605, 606]
}
ECO_SUB_MAP = {
    1:[101, 102, 103, 104, 105, 109],
    2:[106, 107, 108, 110],
    3:[201,202,203,204],
    4:[401,402,403],
    5:[404,405,406,407],
    6:[301],
    7:[302],
    8:[111,112],
    16:[602]
}

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx


def processRow(row, paras, out_path):
    # GRASS_ID = [201,202,203,204,205]
    pid = os.getpid()
    
    old00, new00 = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    logging.info(f"Process {pid} is starting {name}")

    oname_cls = path.join(out_path, name + '.tif')
    # oname_patch = path.join(out_path, 'patch_'+name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname_cls):
        return
    
    with rio.open(old00) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 4
        meta['driver'] = 'COG'
        meta['OVERVIEW_RESAMPLING']='NEAREST'
        meta['dtype'] = 'uint8'
        # meta['descriptions'] = 

    e = (xmin, ymin, xmax, ymax)
    old00_array = get_tiled_imarray(old00, xmin, ymin, xmax, ymax)[0]
    new00_array = get_tiled_imarray(new00, xmin, ymin, xmax, ymax)[0]

    result1 = np.isin(new00_array,ECO_SUB_MAP[3]) & ~np.isin(old00_array, ECO_SUB_MAP[3])
    result2 = ~np.isin(new00_array,ECO_SUB_MAP[4]) & np.isin(old00_array, ECO_SUB_MAP[4])
    result3 = ~np.isin(new00_array,ECO_SUB_MAP[7]) & np.isin(old00_array, ECO_SUB_MAP[7])
    result4 = ~np.isin(new00_array,ECO_SUB_MAP[16]) & np.isin(old00_array, ECO_SUB_MAP[16])

    result = np.stack([result1, result2, result3, result4], axis=0).astype(np.uint8)
    if result.max() < 1:
        return

    meta['height'], meta['width'] = result1.shape
    
    with rio.open(oname_cls,'w',**meta) as dst:
        dst.write(result1*255,1)
        dst.write(result2*255,2)
        dst.write(result3*255,3)
        dst.write(result4*255,4)
        dst.descriptions = tuple(['grass add', 'wet drop', 'crop drop', 'bare drop'])
    logging.info(f"Process {pid} finished {name}")




if __name__ =='__main__':
    old00 = '/mnt/Netapp/longtf/!eco.system/landcover/base/2000lc_30m_type2015.tif'
    new00 = '/mnt/Netapp/longtf/!eco.system/landcover/cn/2000_lc_30m.tif'

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn_r1/diff00'
    paras = (old00, new00)
    if not path.exists(out_path):
        makedirs(out_path)

    # 配置日志
    log_filename = path.join(out_path, f"log_{datetime.now().strftime('%Y%m%d')}.log")  # 日志文件按日期命名
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
        datefmt="%Y-%m-%d %H:%M",  # 日期格式精确到分钟
        handlers=[
            logging.FileHandler(log_filename, mode='a', encoding='utf-8'),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    # for i, prop in gdf.iterrows():
    #     processRow((i, prop), paras=paras, out_path=out_path)
    # processRow((0,gdf.iloc[0]), paras=paras, out_path=out_path)

    p = Pool(50)
    p.imap(partial(processRow, paras=paras, out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt diff00.vrt *.tif'
    os.system(cmd)
    # cmd = 'gdalbuildvrt patch_2010.vrt patch*.tif'
    # os.system(cmd)