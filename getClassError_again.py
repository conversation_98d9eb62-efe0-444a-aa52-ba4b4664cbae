
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def getErrorRow(row, raster_ref_path, cls_path, out_path, wsize=110, step=20):
    half_win = int(wsize/2)
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    inner_oname = path.join(out_path, 'inner_' + name + '.tif')
    among_oname = path.join(out_path, 'among_'+ name + '.tif')
    if path.exists(inner_oname)and path.exists(among_oname):
        return
    with rio.open(raster_ref_path) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
    im_array = get_tiled_imarray(raster_ref_path, xmin, ymin, xmax, ymax)
    cls_array = get_tiled_imarray(cls_path, xmin, ymin, xmax, ymax)
    mask = cls_array==0
    meta['height'], meta['width'] = im_array.shape[1:]
    cls_ids = np.unique(cls_array)
    # 生成距离矩阵
    # cls_means = [np.nanmean(im2020[:,cls2020[0]==c], axis=1) for c in cls_ids]
    cls_median = [np.nanmedian(im_array[:,cls_array[0]==c], axis=1) for c in cls_ids]
    cls_dist = cdist(cls_median, cls_median, metric='euclidean')
    cls_dict_df = pd.DataFrame(cls_dist,index=cls_ids, columns=cls_ids)

    inner_bias_array = np.zeros_like(cls_array,dtype=np.float32)
    among_bias_array = np.ones_like(cls_array,dtype=np.float32)


    for cid, c_center in zip(cls_ids, cls_median):
        im_cls_win_mean = np.zeros([im_array.shape[0], im_array.shape[1]-wsize,im_array.shape[2]-wsize])
        for ystart, xstart in slides_of_imarray(im_array, wsize, step):
            # print(ystart, xstart)
            win_im = im_array[:, ystart:ystart+wsize, xstart:xstart+wsize]
            win_cls = cls_array[0, ystart:ystart+wsize, xstart:xstart+wsize]
            this_im_win_mean = im_cls_win_mean[:, ystart:ystart+step, xstart:xstart+step]
            if np.sum(win_cls==cid)<4:
                im_cls_win_mean[:, ystart:ystart+step, xstart:xstart+step] = np.where(~np.isnan(this_im_win_mean), this_im_win_mean, np.nan)
            else:
                im_cls_win_mean[:, ystart:ystart+step, xstart:xstart+step] = np.nanmedian(win_im[:, win_cls==cid],axis=1)[:,np.newaxis,np.newaxis]
            # break
        # break
        cls_win_mean_pad = np.pad(im_cls_win_mean, pad_width=[(0,0), (half_win, half_win), (half_win, half_win)], mode='edge')
        inner_delta_local = np.sqrt(np.sum((im_array[:,cls_array[0]==cid] - cls_win_mean_pad[:,cls_array[0]==cid])**2, axis=0))
        inner_delta_global = np.sqrt(np.sum((im_array[:,cls_array[0]==cid] - c_center[:,np.newaxis])**2, axis=0))

        inner_bias_array[cls_array==cid] = np.minimum(inner_delta_local, inner_delta_global)

        among_global_bias = np.sqrt(np.sum((im_array[:,cls_array[0]!=cid] - c_center[:,np.newaxis])**2, axis=0))
        among_this_bias = np.sqrt(np.sum((im_array[:,cls_array[0]!=cid] - cls_win_mean_pad[:,cls_array[0]!=cid])**2, axis=0))
        among_this_bias = np.nan_to_num(among_this_bias,nan=1)
        among_this_bias = np.maximum(among_this_bias, among_global_bias)
        this_cls = cls_array[cls_array !=cid]
        this_cls_dist_base = cls_dict_df[cid][this_cls].to_numpy()
        among_this_bias = among_this_bias/this_cls_dist_base
        among_bias_array[cls_array!=cid] = np.minimum(among_bias_array[cls_array!=cid], among_this_bias)
    among_bias_array[mask] = np.nan
    inner_bias_array[mask] = np.nan
    with rio.open(among_oname,'w',**meta) as dst:
        dst.write(among_bias_array)
    with rio.open(inner_oname,'w',**meta) as dst:
        dst.write(inner_bias_array)



if __name__ =='__main__':
    # raster_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    raster_ref_path = '/mnt/mfs/TRANS/longtf/landsat_2010/2010.vrt'
    # change_mask_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # shp_path = '/mnt/Netapp/longtf/!eco.system/changes/cn_2023_yellow_river/N14E15_change.shp'
    # mask_path = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    cls_path = '/mnt/mfs1/class2/0.ecomap/0.process/cls_update_2010_0.15_0.35_morph_shade_sat0.6_rm8/u2010.vrt'
    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/cls_error_2010_again1'
    makedirs(out_path)


    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/300km.shp"

    gdf = gpd.read_file(tile_def)

    p = Pool(33)
    p.imap(partial(getErrorRow, raster_ref_path=raster_ref_path, cls_path=cls_path, out_path=out_path), gdf.iterrows())
    p.close()
    p.join()