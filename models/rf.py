import pandas as pd
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score

# 1. 加载数据集
data = pd.read_csv('/mnt/mfs1/class2/0.ecomap/0.process/modCropSampls_cn_30x/modCropSampls_cn_merge.csv')  # 替换为你的 CSV 文件路径
X = data.drop(['rs_code', 'system:index','.geo'], axis=1)  # 替换 'target' 为你的目标列名
y = data['rs_code']

# 2. 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 3. 定义随机森林分类器
rf = RandomForestClassifier(random_state=42)

# 4. 定义超参数网格
param_grid = {
    'n_estimators': [250, 400, 600],         # 决策树的数量
    'max_depth': [None, 10, 20, 30],       # 树的最大深度
    'min_samples_split': [2, 5, 10],       # 内部分裂所需最小样本数
    'min_samples_leaf': [1, 2, 4],         # 叶节点所需最小样本数
    'max_features': ['sqrt', 'log2', None] # 每次分裂考虑的最大特征数
}

# 5. 使用 GridSearchCV 寻找最佳超参数
grid_search = GridSearchCV(estimator=rf, param_grid=param_grid, cv=5, scoring='accuracy', verbose=2, n_jobs=-1)
grid_search.fit(X_train, y_train)

# 6. 输出最佳超参数
print("Best parameters found: ", grid_search.best_params_)

# 7. 使用最佳模型评估测试集
best_rf = grid_search.best_estimator_
y_pred = best_rf.predict(X_test)

print("Test Accuracy: ", accuracy_score(y_test, y_pred))
print("\nClassification Report:\n", classification_report(y_test, y_pred))
