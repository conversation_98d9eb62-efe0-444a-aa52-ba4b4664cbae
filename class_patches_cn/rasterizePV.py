
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime
from rasterio.features import rasterize

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx


def processRow(row, paras, out_path):
    # GRASS_ID = [201,202,203,204,205]
    pid = os.getpid()
    
    shp_path, update_pv_path = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    logging.info(f"Process {pid} is starting {name}")

    oname = path.join(out_path, name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    with rio.open(update_pv_path) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['dtype'] = 'uint16'
        meta['nodata'] = 0
    gdf = gpd.read_file(shp_path)
    update_pv_array = get_tiled_imarray(update_pv_path, xmin, ymin, xmax, ymax)[0]




    # 2. 设置目标投影（如WGS84转为UTM，假设目标投影为EPSG:4326）
    gdf = gdf.to_crs(meta['crs'])
    gdf = gdf.cx[xmin:xmax, ymin:ymax]
    shapes = list(zip(gdf.geometry, gdf['Inst_year']))
    if len(shapes) < 1:
        return
    burned = rasterize(
        shapes, 
        out_shape=update_pv_array.shape,
        transform=meta['transform'],
        dtype=np.uint16)
    burned[(burned <= 2020)&(update_pv_array!=1)] = 0

    if burned.max() < 1:
        return
    meta['height'], meta['width'] = burned.shape
    
    with rio.open(oname,'w',**meta) as dst:
        dst.write(burned,1)
    logging.info(f"Process {pid} finished {name}")




if __name__ =='__main__':
    shp = '/mnt/mfs1/class2/0.ecomap/0.process/PV_change/2010_2022.shp'
    update_pv = '/mnt/Netapp/yinry/0.LCLU.DATA/PV_2020/albers/pv_2020.vrt'


    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/pv_change'
    if not path.exists(out_path):
        makedirs(out_path)

    # 配置日志
    log_filename = path.join(out_path, f"log_{datetime.now().strftime('%Y%m%d')}.log")  # 日志文件按日期命名
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
        datefmt="%Y-%m-%d %H:%M",  # 日期格式精确到分钟
        handlers=[
            logging.FileHandler(log_filename, mode='a', encoding='utf-8'),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    # for i, prop in gdf.iterrows():
    #     processRow((i, prop), paras=(shp, update_pv), out_path=out_path)
    # # processRow((0,gdf.iloc[0]), paras=(im), out_path=out_path)

    p = Pool(45)
    p.imap(partial(processRow, paras=(shp, update_pv), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt pv_change.vrt *.tif'
    os.system(cmd)