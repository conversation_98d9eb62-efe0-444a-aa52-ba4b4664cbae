
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def patch_cisc_pv(e, base_cls_array, cisc20_path, cisc22_path, pv_path, cisc20_valid_path, cisc22_valid_path):
    pid = os.getpid()
    logging.info(f"Process {pid} is doing patch_cisc_pv")
    xmin, ymin, xmax, ymax = e
    update_cisc20_array = get_tiled_imarray(cisc20_path, xmin, ymin, xmax, ymax)[0]
    update_cisc22_array = get_tiled_imarray(cisc22_path, xmin, ymin, xmax, ymax)[0]
    cisc20_valid_array = get_tiled_imarray(cisc20_valid_path, xmin, ymin, xmax, ymax)[0]
    cisc22_valid_array = get_tiled_imarray(cisc22_valid_path, xmin, ymin, xmax, ymax)[0]
    update_pv_array = get_tiled_imarray(pv_path, xmin, ymin, xmax, ymax)[0]

    update_cisc20_array[cisc20_valid_array==0] = 0
    update_cisc22_array[cisc22_valid_array==0] = 0
    update_cisc22_mask = morphology.binary_closing(update_cisc22_array, morphology.square(3))

    update_cisc20_array[(update_cisc22_mask==0)&(cisc22_valid_array==1)] = 0
    update_cisc20_array = update_cisc20_array==1
    update_pv_array = (update_pv_array <= 2020) & (update_pv_array > 1)

    # update_cisc_array = morphology.binary_closing(update_cisc_array, morphology.disk(1))
    update_pv_array = morphology.binary_closing(update_pv_array, morphology.disk(2))

    base503 = base_cls_array==503
    # update_cisc_array[base503] = False

    update_cisc20_array = morphology.remove_small_objects(update_cisc20_array, 3, 2)
    update_pv_array = morphology.remove_small_objects(update_pv_array, 3)

    near503 = morphology.binary_dilation(base503, morphology.square(3))
    new503 = update_cisc20_array & base503
    potential503 = update_cisc20_array & (morphology.binary_opening(update_cisc20_array&(new503==0), morphology.square(3))==0)
    new503 = potential503 & near503
    del base503, potential503, near503
    patch_array = np.zeros_like(update_cisc20_array, dtype='uint16')
    patch_array[update_cisc20_array] = 501
    patch_array[update_pv_array] = 502
    patch_array[new503] = 503
    patch_array[base_cls_array==0] = 0
    patch_array[base_cls_array==0] = 0
    return patch_array

def patch110_204(e, base_cls_array, ref_path, ndvi_path, prob20_path):
    pid = os.getpid()
    logging.info(f"Process {pid} is doing patch110_204")
    forest_cls = [101,102,103,104,105,106,107,108,109,110,111,112,113,114]
    grass_cls = [201,202,203,204,205]

    xmin, ymin, xmax, ymax = e
    update_ndvi_array = get_tiled_imarray(ndvi_path, xmin, ymin, xmax, ymax)[0]
    ref2020_cls_array = get_tiled_imarray(ref_path, xmin, ymin, xmax, ymax)[0]
    prob20_array_1 = get_tiled_imarray(prob20_path, xmin, ymin, xmax, ymax)[0]
    prob20_array_2 = get_tiled_imarray(prob20_path, xmin, ymin, xmax, ymax)[1]
    potaintialType = np.where(prob20_array_1>prob20_array_2, 110, 204)
    
    
    update_array = update_ndvi_array > 750
    base_bare = np.isin(base_cls_array, [602, 603, 604, 605])
    acceptable = np.isin(ref2020_cls_array, grass_cls+forest_cls) | (np.maximum(prob20_array_1, prob20_array_2) > 0.5) | (update_ndvi_array > 1000)
    update_array = update_array & acceptable & base_bare

    
    update_array[base_cls_array==0] = False

    buffer204 = morphology.binary_dilation(update_array, morphology.disk(2))
    base_bare[update_array] = False
    smallleft = base_bare & (morphology.binary_opening(base_bare, morphology.disk(2))==False)

    update_array[buffer204&smallleft] = True

    update_array = update_array*potaintialType
    return update_array

def processRow(row, paras, out_path):
    # GRASS_ID = [201,202,203,204,205]
    pid = os.getpid()
    
    valid2020, valid2022, update_cisc2020, update_cisc2022, update_pv, base_cls, ref2020_cls, prob20, prob23, update_ndvi, initpatch_path = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    logging.info(f"Process {pid} is starting {name}")

    oname_cls = path.join(out_path, 'cls_'+name + '.tif')
    oname_patch = path.join(out_path, 'patch_'+name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname_cls) & path.exists(oname_patch):
        return
    
    with rio.open(base_cls) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
    e = (xmin, ymin, xmax, ymax)
    base_array = get_tiled_imarray(base_cls, xmin, ymin, xmax, ymax)[0]
    new_base = base_array.copy()
    patch_array = get_tiled_imarray(initpatch_path, xmin, ymin, xmax, ymax)[0]
    new_base[patch_array>0] = patch_array[patch_array>0]
    patch_isv_pv = patch_cisc_pv(e, new_base, update_cisc2020, update_cisc2022, update_pv, valid2020, valid2022)
    new_base[patch_isv_pv>0] = patch_isv_pv[patch_isv_pv>0]
    patch_110_204 = patch110_204(e, new_base, ref2020_cls, update_ndvi, prob20)
    new_base[patch_110_204>0] = patch_110_204[patch_110_204>0]
    all_patch = np.where(base_array == new_base, 0, new_base)

    
    # if valid_mask.max() < 1:
    #     return
    meta['height'], meta['width'] = all_patch.shape
    
    with rio.open(oname_cls,'w',**meta) as dst, rio.open(oname_patch,'w',**meta) as dst_patch:
        dst.write(new_base,1)
        dst_patch.write(all_patch,1)
    logging.info(f"Process {pid} finished {name}")




if __name__ =='__main__':
    valid2022 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/valid2m/valid2m.vrt'
    valid2020 = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/valid2m_2020/valid2m.vrt'
    update_cisc2020 = '/mnt/mfs1/classT/0.Dataset/CISC_2020/isc_hh.vrt'
    update_cisc2022 = '/mnt/mfs1/classT/0.Dataset/CISC_2022/isc2022_cn_ecolc.vrt'
    update_pv = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/pv_change/pv_change.vrt'

    base_cls = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt'
    ref2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt'

    prob20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2020_hh_prob_1/prob2020.vrt'
    prob23 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2023_hh_prob_1/prob2023.vrt'

    ccd = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    update_ndvi = '/mnt/mfs1/classT/0.DataCollection/composits/ChinaYearlyNoSnow_indexes_p85/index85_hh.vrt'


    initpatch_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patch302_clean_1230/patch302.vrt'

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patches_2022'
    paras = (valid2020, valid2022, update_cisc2020, update_cisc2022, update_pv, base_cls, ref2020_cls, prob20, prob23, update_ndvi, initpatch_path)
    if not path.exists(out_path):
        makedirs(out_path)

    # 配置日志
    log_filename = path.join(out_path, f"log_{datetime.now().strftime('%Y%m%d')}.log")  # 日志文件按日期命名
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
        datefmt="%Y-%m-%d %H:%M",  # 日期格式精确到分钟
        handlers=[
            logging.FileHandler(log_filename, mode='a', encoding='utf-8'),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    # for i, prop in gdf.iterrows():
    #     processRow((i, prop), paras=paras, out_path=out_path)
    # # processRow((0,gdf.iloc[0]), paras=(im), out_path=out_path)

    p = Pool(45)
    p.imap(partial(processRow, paras=paras, out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt cls_2020.vrt cls*.tif'
    os.system(cmd)
    cmd = 'gdalbuildvrt patch_2020.vrt patch*.tif'
    os.system(cmd)