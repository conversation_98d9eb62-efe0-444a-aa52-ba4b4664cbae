
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters
import logging
from datetime import datetime

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def update101_110(update_array, base_array, ref20_array, prob_array):
    logging.info(f"Start update1xx process {os.getpid()}")

    # 去除更新矩阵中林地区域之：细碎的、林概率小的、现有类别为湿地、园地的；
    # 将更新矩阵中林地区域之子类修改为现有产品中临近类型（如果有），没有邻近林地的，修改为111（乔木园地）
    update_cls = [101,102,103,107]
    baseupmask = np.isin(update_array, update_cls)
    prob_mask = prob_array[0] > 0.7
    not_allow_cls = [
        401,402,403,404,405,406,407
        ]
    forest_cls = [101,102,103,104,105,106,107,108,109,110,111,112,113,114]
    cls_mask = ~np.isin(base_array, not_allow_cls+forest_cls)
    clsref_mask = ~np.isin(ref20_array, not_allow_cls)
    upmask = baseupmask & prob_mask & cls_mask & clsref_mask
    if upmask.max() < 1:
        return upmask

    
    upmask = morphology.binary_closing(upmask, morphology.square(3))
    upmask[prob_array[0]  < 0.6] = False
    # upmask_outline = (~morphology.binary_erosion(upmask, morphology.disk(1))) & upmask
    # upmask_filter = morphology.binary_opening(upmask, morphology.square(4))
    # little_mask = (~upmask_filter) & upmask
    # little_mask = np.zeros_like(upmask)
    little_mask = (~morphology.binary_dilation(
        morphology.binary_erosion(upmask, morphology.square(3)),
          morphology.square(5))) & upmask
    # upmask[little_mask] = False

    labeled_mask = morphology.remove_small_objects(upmask & (~little_mask), 21, 2)
    labeled_mask = morphology.label(labeled_mask, connectivity=2)
    # 将去除的小区域添加到小区域掩码中
    little_mask  = little_mask | (upmask & (labeled_mask==0))
    valid_values = [104,105,106,108,109,110,111,112,113,114]

    max_label = labeled_mask.max()

    # 预先计算所有标签的膨胀结果，避免重复调用
    dilated_masks = morphology.dilation(labeled_mask, morphology.square(3))
    pid = os.getpid()
    if max_label > 5000:
        logging.warning(f"Too many 1xx objects ({max_label}) in Process {pid}")

    for label in range(1, max_label + 1):  # 遍历所有标签，跳过背景
        # 提取当前对象的掩码
        this_mask = labeled_mask == label
        # if not np.any(this_mask):  # 如果当前对象为空，跳过
        #     continue
        # 以0.01的概率输出日志
        if np.random.rand() < 0.01:
            logging.info(f"Process {pid} doing {label} of {max_label}, has {len(base_values)} values")
        # # 如果周长面积比过大，跳过
        # if np.sum(upmask_outline & this_mask) / np.sum(this_mask) > 0.8:
        #     # 添加到小区域掩码中
        #     little_mask = little_mask | this_mask
        #     continue

        # 提取当前对象的膨胀掩码
        dilated_mask = dilated_masks == label
        dilated_mask[this_mask] = False
        # dilated_mask = dilated_mask & np.logical_not(this_mask)
        base_values = base_array[dilated_mask]
        filtered_values = base_values[np.isin(base_values, valid_values)]    
        # if np.sum(np.isin(base_values, update_cls)) > 3:  # 如果存在更新矩阵中的类别，跳过
        #     continue
        # else:
        if len(filtered_values) > 3:  # 如果存在足够的有效值
            # 计算众数
            unique_values, counts = np.unique(filtered_values, return_counts=True)
            most_common_value = unique_values[np.argmax(counts)]
            if counts.max() > 4:
                update_array[this_mask] = most_common_value
        elif np.sum(np.isin(base_values, update_cls)) > 3:
            continue
        elif np.sum(np.isin(base_values, [301, 302])) > 3:
            update_array[this_mask] = 111

    footprint = morphology.disk(6)
    base_forest = np.isin(base_array, forest_cls)
    local_forest_id = filters.rank.modal(base_array, footprint, mask = base_forest)
    update_array[little_mask] = local_forest_id[little_mask]
    # upmask = upmask_filter | little_mask
    return upmask

def update112(update_array, base_array, ref20_array, prob_array):
    logging.info(f"Start update112 process {os.getpid()}")

    # 把更新矩阵中一部分301、302修改为112（灌木园地）,不筛选更新矩阵
    croplist = [301,302]
    upmask = np.isin(update_array, croplist) & (np.isin(base_array, croplist) | np.isin(ref20_array, croplist))
    upmask = upmask & (prob_array[0] > 0.69)
    if upmask.max() < 1:
        return upmask   
    upmask = morphology.binary_closing(upmask, morphology.square(2))
    upmask[prob_array[0] < 0.6] = False
    upmask = morphology.remove_small_objects(upmask,10,2)
    update_array[upmask] = 112
    return upmask

def update2xx(update_array, base_array, ref20_array, prob_array):
    logging.info(f"Start update2xx process {os.getpid()}")

    # 理论上可靠的草地来源应是分类为草、三类概率中草的概率较高；实际上三类概率可能存在异常；
    # 在两个2020产品不一致的地方，更能接受更改；（两个产品一致的地方应该更谨慎的修改）
    GRASS_ID = [201,202,203,204,205]
    allow_src = [101, 102, 103, 107, 301, 302, 501, 502]

    base_grass = np.isin(base_array, GRASS_ID)
    ref_grass = np.isin(ref20_array, GRASS_ID)
    

    base_allow = np.isin(base_array, allow_src)
    # ref_allow = np.isin(ref20_array, allow_src)
    # upmask = np.isin(update_array, GRASS_ID) & ( base3 | ref3 )
    upmask = np.isin(update_array, GRASS_ID) & base_allow
    if upmask.max() < 1:
        return upmask
    # 使用非，避免遗漏无效值区域
    upmask[(~ref_grass) & (~(prob_array[1]>0.45))] = False
    upmask[(ref_grass) & (~(prob_array[1]>0.32))] = False
    upmask = morphology.binary_closing(upmask, morphology.square(3))
    footprint = morphology.disk(160)
    local_grass_id = filters.rank.modal(base_array, footprint, mask = base_grass)
    update_array[upmask] = local_grass_id[upmask]
    # np.where()
    return upmask

def update3xx(update_array, base_array, ref20_array, prob_array):
    logging.info(f"Start update3xx process {os.getpid()}")
    crop_ID = [301,302]
    # allow_src = [101, 102, 103, 107, 301, 302]
    base3out = ~np.isin(base_array, crop_ID)
    # ref3out = ~np.isin(ref20_array, crop_ID)
    init_up = np.isin(update_array, crop_ID)
    upmask = init_up & base3out

    if upmask.max() < 1:
        return upmask

    rebase_array = np.zeros_like(base_array)
    reref20_array = np.zeros_like(ref20_array)
    for k, v in CLS_MAP.items():
        rebase_array[np.isin(base_array, v)] = k
        reref20_array[np.isin(ref20_array, v)] = k
    # 使用非，避免遗漏无效值区域; upmask中已经无农田区域
    upmask[(rebase_array==reref20_array) & (~(prob_array[2]>0.5))] = False
    upmask[~(prob_array[2]>0.35)] = False

    upmask = morphology.binary_closing(upmask, morphology.square(2))
    upmask = upmask & init_up
    return upmask 


def processRow(row, paras, out_path):
    # GRASS_ID = [201,202,203,204,205]
    pid = os.getpid()
    
    update_cls, base_cls, ref2020_cls, prob20, prob23, ccd = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    logging.info(f"Process {pid} is starting {name}")

    oname = path.join(out_path, name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    with rio.open(base_cls) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    # union_recls_array = np.zeros_like(cls_array)
    update_cls_array = get_tiled_imarray(update_cls, xmin, ymin, xmax, ymax)[0].astype(np.uint16)
    base_cls_array = get_tiled_imarray(base_cls, xmin, ymin, xmax, ymax)[0]
    ref2020_cls_array = get_tiled_imarray(ref2020_cls, xmin, ymin, xmax, ymax)[0]
    prob20_array = get_tiled_imarray(prob20, xmin, ymin, xmax, ymax)
    prob23_array = get_tiled_imarray(prob23, xmin, ymin, xmax, ymax)
    # ccd_array = get_tiled_imarray(ccd, xmin, ymin, xmax, ymax)[0]
    prob_array = np.fmin(prob20_array, prob23_array)

    update_cls_array[base_cls_array == 0] = 0
    if update_cls_array.max() < 1:
        return
    logging.info(f"Process {pid} is preprocessing {name}")
    newcls_labelled = morphology.label(update_cls_array, connectivity=1)
    small_mask = (update_cls_array  > 0) & (morphology.remove_small_objects(newcls_labelled, 4) == 0)
    del newcls_labelled
    footprint = morphology.disk(2)
    modal_cls = filters.rank.modal(update_cls_array, footprint)
    update_cls_array[small_mask] = modal_cls[small_mask]
    del modal_cls

    uppara = (update_cls_array, base_cls_array, ref2020_cls_array, prob_array)
    upmask = update101_110(*uppara) | update112(*uppara) | update2xx(*uppara) | update3xx(*uppara)  

    update_cls_array[~upmask] = 0

    if update_cls_array.max() < 1:
        return


    meta['height'], meta['width'] = update_cls_array.shape
    
    with rio.open(oname,'w',**meta) as dst:
        dst.write(update_cls_array,1)
    logging.info(f"Process {pid} finished {name}")




if __name__ =='__main__':
    update_cls = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patch302_cn/patch302_cn.tif'
    base_cls = '/mnt/Netapp/longtf/!eco.system/changes/cn_2020/lc_index.vrt'
    ref2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt'
    prob20 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2020_hh_prob_1/prob2020.vrt'
    prob23 = '/mnt/mfs1/class2/0.ecomap/0.process/0.treenew2020-2023_hh/treenew2023_hh_prob_1/prob2023.vrt'
    ccd = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/0.update_cn/patch302_clean_dropfalseIS_0104'
    if not path.exists(out_path):
        makedirs(out_path)

    # 配置日志
    log_filename = path.join(out_path, f"log_{datetime.now().strftime('%Y%m%d')}.log")  # 日志文件按日期命名
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
        datefmt="%Y-%m-%d %H:%M",  # 日期格式精确到分钟
        handlers=[
            logging.FileHandler(log_filename, mode='a', encoding='utf-8'),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    # for i, prop in gdf.iterrows():
    #     processRow((i, prop), paras=(update_cls, base_cls, ref2020_cls, prob20, prob23, ccd), out_path=out_path)
    # processRow((0,gdf.iloc[0]), paras=(update_cls, base_cls, ref2020_cls, prob20, prob23, ccd), out_path=out_path)

    p = Pool(45)
    p.imap(partial(processRow, paras=(update_cls, base_cls, ref2020_cls, prob20, prob23, ccd), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt patch302.vrt *.tif'
    os.system(cmd)