
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from skimage import morphology, filters


def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    GRASS_ID = [201,202,203,204,205]
    update_cls, base_cls, ref2020_cls, allow_src_dict = paras
    i, prop = row
    xmin, ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']

    oname = path.join(out_path, name + '.tif')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    with rio.open(update_cls) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        
    # recls_array_bits = np.zeros((len(cls_path_list), meta['height'], meta['width']), dtype=np.uint8)
    # union_recls_array = np.zeros_like(cls_array)
    update_cls_array = get_tiled_imarray(update_cls, xmin, ymin, xmax, ymax)[0]
    base_cls_array = get_tiled_imarray(base_cls, xmin, ymin, xmax, ymax)[0]
    # ref2020_cls_array = get_tiled_imarray(ref2020_cls, xmin, ymin, xmax, ymax)[0]

    newcls_labelled = morphology.label(update_cls_array, connectivity=1)
    small_mask = (update_cls_array  > 0) & (morphology.remove_small_objects(newcls_labelled, 4) == 0)
    del newcls_labelled
    footprint = morphology.disk(2)
    modal_cls = filters.rank.modal(update_cls_array, footprint)
    update_cls_array[small_mask] = modal_cls[small_mask]
    del modal_cls
    


    # 忽略草地变化
    base_grass = np.isin(base_cls_array, GRASS_ID)
    update_grass = np.isin(update_cls_array, GRASS_ID)
    update_cls_array[update_grass & base_grass] = 0
    update_grass[base_grass] = 0
    footprint = morphology.disk(300)
    local_grass_id = filters.rank.modal(base_cls_array, footprint, mask = base_grass)
    update_cls_array[update_grass] = local_grass_id[update_grass]
    del base_grass, update_grass

    

    # 限制变化情况
    for k, v in allow_src_dict.items():
        update_cls_array[(~np.isin(update_cls_array, v)) & base_cls_array==k] = 0

    update_cls_array[update_cls_array==base_cls_array] = 0
    

    if update_cls_array.max() < 1:
        return
    meta['height'], meta['width'] = update_cls_array.shape
    
    with rio.open(oname,'w',**meta) as dst:
        dst.write(update_cls_array,1)




if __name__ =='__main__':
    update_cls = '/mnt/mfs1/class2/0.ecomap/0.process/lc_updatecrop_2020_a302/a302.vrt'
    base_cls = '/mnt/Netapp/longtf/!eco.system/landcover/lc_hh_2020.tif'
    ref2020_cls = '/mnt/Netapp/longtf/!eco.system/landcover/base/2020lc_30m_type2015.vrt'

    allow_src_dict = {
        102:[107, 201, 203, 204, 302],
        107:[201, 203, 204, 302],
        201:[302],
        203:[302],
        204:[302],
        302:[107, 201, 203, 204]
    }

    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/patch302'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/china_albers_240×1024m_grid_land.shp"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(update_cls, base_cls, ref2020_cls, allow_src_dict), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(update_cls, base_cls, ref2020_cls, allow_src_dict), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    os.chdir(out_path)
    cmd = 'gdalbuildvrt patch302.vrt *.tif'
    os.system(cmd)