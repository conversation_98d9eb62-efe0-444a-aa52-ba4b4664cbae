
import rasterio as rio
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
from sklearn.neighbors import KNeighborsClassifier
from tqdm import tqdm
from skimage import morphology, filters
from skimage.morphology import disk, binary_closing, remove_small_objects

CLS_MAP  = {
    1:[101, 102, 103, 104, 105, 111, 112, 113],
    2:[106, 107, 108, 114],
    3:[109, 110, 204],
    4:[201,202,203,205],
    5:[301, 302],
    6:[404,405,406,407],
    7:[401,402,403],
    8:[501,502,503,504],
    9:[602,603,604]   
}
def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in tqdm(range(0, lasty, step)):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    for xstart in range(0,lastx, step):
        yield lasty, xstart
    yield lasty, lastx

def updateRow(row, cls_path, cls_old_path, out_path, ccd_path, wsize=180,step=80, n_neighbors=10):
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    # inner_name = path.join(error_path, 'inner_' + name + '.tif')
    # among_name = path.join(error_path, 'among_'+ name + '.tif')

    cls_oname = path.join(out_path, name + '.tif')
    mask_oname = path.join(out_path, 'mask_' + name + '.tif')
    # up10_oname = path.join(out_path, 'up10mask_' + name + '.tif')
    # up00_oname = path.join(out_path, 'up00mask_' + name + '.tif')
    if path.exists(cls_oname) :
        return
    
    with rio.open(cls_path) as src:
        transform = src.transform
        row_start, col_start = src.index(xmin, ymax)
        transform_local = transform * rio.Affine.translation(col_start, row_start)
        meta = src.meta.copy()
        meta['transform'] = transform_local
        meta['compress'] = 'LZW'
        meta['count'] = 1
        meta['driver'] = 'COG'
        meta['OVERVIEW_RESAMPLING']='NEAREST'
    

    cls_array = get_tiled_imarray(cls_path, xmin, ymin, xmax, ymax)
    clsold_array = get_tiled_imarray(cls_old_path, xmin, ymin, xmax, ymax)
    ccd_array = get_tiled_imarray(ccd_path, xmin, ymin, xmax, ymax)
    newcls_array = cls_array.copy()
    recls_array = np.zeros_like(cls_array)
    reclsold_array = np.zeros_like(cls_array)
    for k, v in CLS_MAP.items():
        recls_array[np.isin(cls_array, v)] = k
        reclsold_array[np.isin(clsold_array, v)] = k
    up_mask = clsold_array != cls_array
    ignore_mask = (recls_array == reclsold_array) & up_mask & (recls_array != 0)
    newcls_array[ignore_mask] = clsold_array[ignore_mask]
    up_mask[ignore_mask] = False

    change_mask = ccd_array[0] > 2010
    newcls_array[0, ~change_mask] = clsold_array[0, ~change_mask]
    up_mask[0, ~change_mask] = False

    newcls_labelled = morphology.label(newcls_array[0], connectivity=1)
    small_mask = (newcls_array[0] > 0) & (remove_small_objects(newcls_labelled, 3) == 0)
    footprint = disk(2)

    up_close = binary_closing(up_mask[0],footprint)
    small_mask = small_mask & up_close


    modal_cls = filters.rank.modal(cls_array[0], footprint)
    newcls_array[0, small_mask] = modal_cls[small_mask]


    meta['height'], meta['width'] = newcls_array.shape[1:]
    
    
    with rio.open(cls_oname,'w',**meta) as dst:
        dst.write(newcls_array)



if __name__ =='__main__':
    # raster_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # raster_ref_path = '/mnt/mfs/TRANS/longtf/landsat_2010/2010.vrt'
    # change_mask_path = '/mnt/mfs/TRANS/longtf/landsat_2023/2023.vrt'
    # shp_path = '/mnt/Netapp/longtf/!eco.system/changes/cn_2023_yellow_river/N14E15_change.shp'
    # mask_path = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2020_2023/2020_2023.vrt'
    cls_path = '/mnt/mfs1/class2/0.ecomap/0.process/cls_update_2020_0.2_0.35_morph_shade_sat0.6_rm8/u2020.vrt'
    cls_old_path = '/mnt/mfs1/class2/0.ecomap/0.process/cls_update_refine_2015_r2/u2015.vrt'
    # cls00_path = '/mnt/Netapp/longtf/!eco.system/landcover/2000lc_30m_type2015.tif'
    ccd_path = '/mnt/mfs/TRANS/longtf/landsat_ccdc_2015_2020/ccdc_2015_2020.vrt'
    # error_path = '/mnt/mfs1/class2/0.ecomap/0.process/cls_error_2010'
    # shade_path = '/mnt/mfs/TRANS/0.eco_shade/shade.vrt'
    out_path = '/mnt/mfs1/class2/0.ecomap/0.process/cls_update_refine_2020'

    if not path.exists(out_path):
        makedirs(out_path)


    tile_def = "/mnt/mfs1/class2/0.ecomap/0.grid/300km.shp"

    gdf = gpd.read_file(tile_def)

    # row = (0,
    #        {'left':2588190, 'bottom': 4914510, 'right': 2589030, 'top': 4915590, 'name':'test'} )
    # updateRow(row, cls00_path=cls00_path, cls10_path=cls10_path, cls10_old_path=cls10_old_path, ccd_path=ccd_path, out_path=out_path)

    # for row in gdf.iterrows():
    #     updateRow(row, cls_path=cls_path, cls_old_path=cls_old_path, ccd_path=ccd_path, out_path=out_path)
    
    p = Pool(59)
    p.imap(partial(updateRow, cls_path=cls_path, cls_old_path=cls_old_path, ccd_path=ccd_path, out_path=out_path), gdf.iterrows())
    p.close()
    p.join()