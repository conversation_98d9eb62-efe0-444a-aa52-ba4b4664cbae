
import rasterio as rio
from rasterio.transform import xy
from rasterio.warp import transform as transform_crs
from rasterio.windows import Window
import matplotlib.pyplot as plt
from os import path, makedirs
from scipy.spatial.distance import cdist
import pandas as pd
import geopandas as gpd
from glob import glob
from multiprocessing import Pool
from functools import partial
import geopandas as gpd
import numpy as np
import os
from shapely.geometry import Point

import geopandas as gpd
from shapely.geometry import Point # 主要用于类型提示或创建示例数据

from scipy.spatial import cKDTree

def filter_points_by_distance_kdtree(points, min_distance):
    """
    使用K-D树贪心算法过滤点集，确保任意两点间欧氏距离大于min_distance。

    参数:
    points (list of lists/tuples or np.ndarray): 输入的点列表，例如 [[x1,y1], [x2,y2],...]
                                                 或 NumPy 数组。
    min_distance (float): 要求的最小欧氏距离。

    返回:
    list of lists: 过滤后的点列表。
    """
    if not points:  # 处理输入为空列表的情况
        return

    # 确保points是NumPy数组，以便cKDTree和向量化操作
    points_np = np.array(points)
    
    # 处理输入为单个点（例如 [x,y,z] 而非 [[x,y,z]]）的情况
    if points_np.ndim == 1:
        points_np = np.array([points_np])
    
    # 如果转换后points_np为空 (例如，原始输入是 [] 或类似结构)
    if points_np.shape == 0:
        return

    filtered_points_list =[]
    # 无条件添加第一个点。这确保了如果输入非空，K-D树总是有数据。
    filtered_points_list.append(points_np[0])

    # 从第二个点开始遍历剩余的点
    for i in range(1, points_np.shape[0]):
        candidate_point = points_np[i]
        
        # filtered_points_list 此时保证至少包含一个点。
        # 将已过滤点列表（其中元素是NumPy数组）转换为一个2D NumPy数组以构建cKDTree。
        tree_data = np.vstack(filtered_points_list)
        kdtree = cKDTree(tree_data)
        
        # 查询K-D树：找到tree_data中（即已过滤点中）
        # 与candidate_point距离小于或等于min_distance的点（包含边界）。
        # p=2 指定欧氏距离。
        # 对于单个查询点，query_ball_point返回一个包含邻近点索引的简单列表。
        indices_too_close = kdtree.query_ball_point(candidate_point, r=min_distance, p=2) # [14]
        
        # 如果indices_too_close为空列表，意味着没有已选点与候选点的距离
        # 小于或等于min_distance。换句话说，所有已选点与候选点的距离
        # 都严格大于min_distance。这满足了过滤条件。
        if not indices_too_close:  # 等价于 len(indices_too_close) == 0
            filtered_points_list.append(candidate_point)
            
    # 如果需要，将结果转换回列表的列表格式
    return [list(p) for p in filtered_points_list]

ECO_MAP = {
    1: [101, 102, 103, 104, 105, 109],
    2: [106, 107, 108, 110],
    3: [201,202,203,204],
    4: [401, 402, 403, 404, 405, 406, 407],
    5: [111, 112, 301, 302],
    6: [113, 114, 205, 501, 502, 503, 504],
    7: [604],
    8: [601, 602, 603, 605, 606]
}

CLIST = []
for k, v in ECO_MAP.items():
    CLIST.extend(v)

def get_window(src, minx, miny, maxx, maxy):
    row_start, col_start = src.index(minx, maxy)
    row_end, col_end = src.index(maxx, miny)
    window = Window(col_start, row_start, col_end - col_start, row_end - row_start)
    return window

def get_tiled_imarray(fn, xmin, ymin, xmax, ymax):
    with rio.open(fn) as src:
        window = get_window(src, xmin, ymin, xmax, ymax)
        imarray = src.read(window=window, boundless=True)
        return imarray

def slides_of_imarray(imarray:np.ndarray, wsize, step):
    c, h, w = imarray.shape
    lastx = w-wsize
    lasty = h-wsize
    for ystart in range(0, lasty, step):
        for xstart in range(0,lastx, step):
            yield ystart, xstart
        yield ystart, lastx
    yield lasty, lastx

def processRow(row, paras, out_path):
    vrt_cls = paras
    i, prop = row
    xmin,ymin, xmax, ymax = prop['left'],prop['bottom'],prop['right'],prop['top']
    name = prop['name']
    n_samples = 35

    oname = path.join(out_path, 'sample_' + name + '.shp')
    # score_oname = path.join(out_path, 'sta_'+ name + '.tif')
    if path.exists(oname):
        return
    
    transform_local = None
    all_geometry = []
    all_id = []
    cls_array = get_tiled_imarray(vrt_cls, xmin, ymin, xmax, ymax)[0]
    local_clist = np.unique(cls_array)
    for k in local_clist:
        if transform_local is None:
            with rio.open(vrt_cls) as src:
                transform = src.transform
                row_start, col_start = src.index(xmin, ymax)
                transform_local = transform * rio.Affine.translation(col_start, row_start)
                crs = src.crs
        if cls_array.max()<1:
            continue
        ones_positions = np.argwhere(cls_array == k)
        if len(ones_positions) >= n_samples:
            selected_indices = np.random.choice(len(ones_positions), n_samples, replace=False)
            selected_positions = ones_positions[selected_indices]
        else:
            selected_positions = ones_positions
        xs, ys = xy(transform_local, selected_positions[:, 0], selected_positions[:, 1])
        
        points = filter_points_by_distance_kdtree(list(zip(xs, ys)), 1500)
        xs, ys = list(zip(*points))
        longs, lats = transform_crs(crs, 'epsg:4326', xs, ys)
        geometry = [Point(xy) for xy in zip(longs, lats)]
        all_geometry.extend(geometry)
        all_id.extend([k]*len(geometry))
    gdf = gpd.GeoDataFrame(data={'rs_code': all_id}, geometry=all_geometry, crs="EPSG:4326")
    if len(gdf) == 0:
        return
    gdf['LONG'] = gdf.geometry.x
    gdf['LAT'] = gdf.geometry.y
    gdf.to_file(oname)





if __name__ =='__main__':
    new20 = '/mnt/mfs1/class2/0.ecomap/0.cn_result/2020_lc_30m.tif'

    # out_path = '/mnt/mfs1/class2/0.ecomap/0.neimeng/2.samples/ValSamples_20250527'
    out_path = '/mnt/mfs1/class2/0.ecomap/0.neimeng/2.samples/ValSamples_20250527_filter1p5km'
    if not path.exists(out_path):
        makedirs(out_path)

    tile_def = "/mnt/mfs1/class2/0.ecomap/0.neimeng/0.shp/neimeng_china_albers_240×1024m_grid_land.gpkg"

    gdf = gpd.read_file(tile_def)

    processRow((0,gdf.iloc[0]), paras=(new20), out_path=out_path)

    p = Pool(36)
    p.imap(partial(processRow, paras=(new20), out_path=out_path), gdf.iterrows())
    p.close()
    p.join()
    # os.chdir(out_path)
    # cmd = 'gdalbuildvrt intersect302.vrt *.tif'
    # os.system(cmd)